# OnlyOffice Editor Integration Documentation

## Overview

This document provides comprehensive information about the OnlyOffice Document Server integration in the GRC Policy Management System. The integration allows users to create, edit, and collaborate on policy documents directly within the web application.

## Architecture

### Components
- **OnlyOffice Document Server**: Docker container running the document server
- **Backend API**: Node.js/Express controllers and routes for OnlyOffice integration
- **Frontend Editor**: React/Next.js component for embedding the editor
- **Document Storage**: File system storage for policy documents

### Integration Flow
```
Frontend Editor → Backend API → OnlyOffice Document Server → Document Storage
     ↑                                                              ↓
     ←─────────────── Callback/Save Events ←─────────────────────────
```

## Backend Implementation

### Controller: `onlyofficeController.js`

#### Key Functions

**1. `getConfig(req, res)`**
- Generates OnlyOffice editor configuration
- Sets document permissions based on policy status
- Configures JWT authentication if enabled
- Returns editor configuration object

**2. `getDocumentContent(req, res)`**
- Serves document content to OnlyOffice
- Validates policy and attachment existence
- Streams file content with appropriate headers

**3. `handleCallback(req, res)`**
- Processes callbacks from OnlyOffice Document Server
- Handles document save events (status 2)
- Updates document metadata and version history
- Manages version numbering system

**4. `createDocument(req, res)`**
- Creates new documents from templates
- Generates unique filenames
- Initializes document metadata
- Sets up version history

### Routes: `onlyoffice.js`

```javascript
// Get OnlyOffice configuration for a document
GET /api/onlyoffice/config/:policyId

// Get document content for OnlyOffice
GET /api/onlyoffice/documents/:policyId/content

// Handle callback from OnlyOffice
POST /api/onlyoffice/callback/:policyId

// Create new document
POST /api/onlyoffice/documents/:policyId/create
```

### Document Templates: `documentTemplates.js`

**Supported File Types:**
- DOCX (Word documents)
- XLSX (Excel spreadsheets)
- PPTX (PowerPoint presentations)
- PDF (Portable Document Format)
- TXT (Plain text)

**Template System:**
- Templates stored in `backend/src/templates/`
- Fallback to empty file creation if template doesn't exist
- MIME type detection based on file extension

## Frontend Implementation

### Editor Component: `page.tsx`

**Location:** `frontend/src/app/policies/editor/[policyId]/page.tsx`

**Key Features:**
- Dynamic OnlyOffice API script loading
- Editor initialization with configuration
- Proper cleanup on component unmount
- Error handling and loading states

**TypeScript Declarations:**
```typescript
declare global {
  interface Window {
    DocsAPI: {
      DocEditor: new (id: string, config: any) => {
        destroyEditor: () => void;
      };
    };
  }
}
```

### Integration Points

**Policy Details Modal:**
- Document creation functionality
- Integration with policy management workflow
- Toast notifications for user feedback

## Docker Configuration

### Production Setup: `docker-compose.yml`

```yaml
services:
  onlyoffice-documentserver:
    image: onlyoffice/documentserver:latest
    container_name: onlyoffice-documentserver
    ports:
      - "8080:80"
    environment:
      - JWT_ENABLED=true
      - JWT_SECRET=${JWT_SECRET}
      - DOC_SERV_MAX_FILE_SIZE=50000000
      - DOC_SERV_TIMEOUT=120000
      - ALLOW_PRIVATE_IP_ADDRESS=true
      - ALLOW_META_IP_ADDRESS=true
```

### Development Setup: `docker-compose.dev.yml`

```yaml
services:
  onlyoffice-documentserver:
    image: onlyoffice/documentserver:latest
    container_name: onlyoffice-documentserver-dev
    ports:
      - "8080:80"
    environment:
      - JWT_ENABLED=false  # Disabled for development
      - JWT_SECRET=
```

## Configuration

### Environment Variables

**Backend (.env):**
```bash
# OnlyOffice Configuration
ONLYOFFICE_CALLBACK_URL=http://host.docker.internal:5000
ONLYOFFICE_JWT_SECRET=your-jwt-secret-here

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads
```

**Frontend (.env.local):**
```bash
NEXT_PUBLIC_API_URL=http://localhost:5000
```

### OnlyOffice Document Server Settings

**Security Settings:**
- JWT authentication (configurable)
- Private IP address access allowed
- HTTPS insecure connections allowed (development)
- Unauthorized storage enabled

**File Limits:**
- Maximum file size: 50MB
- Request timeout: 120 seconds

**Network Configuration:**
- Docker network bridge mode
- Host access via `host.docker.internal`

## Document Management

### File Storage Structure
```
backend/uploads/policies/
├── policy-1234567890-123456789.docx
├── policy-1234567891-987654321.xlsx
└── ...
```

### Version Control System

**Version Numbering:**
- Initial version: 1.0
- Incremental updates: +0.1 (1.1, 1.2, etc.)
- Version history tracking with metadata

**Version History Schema:**
```javascript
{
  version: "1.1",
  modifiedBy: {
    id: "user_id",
    name: "User Name"
  },
  modifiedAt: Date,
  size: 12345,
  action: "created" | "modified"
}
```

### Document Permissions

**Based on Policy Status:**
- **Draft**: Full edit permissions
- **Other statuses**: View-only mode

**Permission Configuration:**
```javascript
permissions: {
  edit: policy.status === 'Draft',
  download: true,
  review: true,
  print: true,
  comment: true
}
```

## API Integration

### Configuration Request Flow

1. Frontend requests editor config: `GET /api/onlyoffice/config/:policyId`
2. Backend validates policy and user permissions
3. Backend generates document key and URLs
4. Backend returns OnlyOffice configuration object
5. Frontend initializes editor with configuration

### Document Save Flow

1. User saves document in OnlyOffice editor
2. OnlyOffice sends callback: `POST /api/onlyoffice/callback/:policyId`
3. Backend downloads updated document from OnlyOffice
4. Backend updates file system and database metadata
5. Backend increments version number
6. Backend responds with success status

### Error Handling

**Backend Error Responses:**
- 404: Policy or document not found
- 400: Invalid request or policy status
- 500: Server errors during document operations

**Frontend Error Handling:**
- Loading states during configuration fetch
- Error messages for failed operations
- Graceful fallback for missing OnlyOffice API

## Security Considerations

### Authentication
- JWT-based user authentication
- Policy-level access control
- Document permissions based on policy status

### File Security
- Unique filename generation
- File type validation
- Size limitations
- Secure file serving

### Network Security
- Docker network isolation
- Configurable JWT for OnlyOffice communication
- CORS configuration for cross-origin requests

## Troubleshooting

### Common Issues

**1. OnlyOffice API Not Loading**
- Check Docker container status
- Verify port 8080 accessibility
- Ensure script URL is correct

**2. Document Save Failures**
- Check callback URL configuration
- Verify file permissions
- Monitor Docker container logs

**3. Permission Errors**
- Validate user authentication
- Check policy status
- Verify document ownership

### Debug Commands

```bash
# Check OnlyOffice container status
docker ps | grep onlyoffice

# View OnlyOffice logs
docker logs onlyoffice-documentserver

# Check file permissions
ls -la backend/uploads/policies/

# Test API endpoints
curl -H "Authorization: Bearer <token>" \
  http://localhost:5000/api/onlyoffice/config/<policyId>
```

## Development Setup

### Prerequisites
- Docker and Docker Compose
- Node.js (v16+)
- MongoDB instance

### Installation Steps

1. **Start OnlyOffice Document Server:**
```bash
cd docker
docker-compose -f docker-compose.dev.yml up -d
```

2. **Configure Backend:**
```bash
cd backend
npm install
cp .env.example .env
# Edit .env with OnlyOffice settings
npm run dev
```

3. **Configure Frontend:**
```bash
cd frontend
npm install
cp .env.local.example .env.local
npm run dev
```

4. **Verify Integration:**
- Access application at http://localhost:3000
- Create or open a policy
- Click "Create Document" or "Edit Document"
- Verify OnlyOffice editor loads correctly

### Testing

**Manual Testing:**
1. Create new policy document
2. Edit document content
3. Save and verify version increment
4. Test different file formats
5. Verify permissions based on policy status

**API Testing:**
```bash
# Test configuration endpoint
curl -X GET "http://localhost:5000/api/onlyoffice/config/POLICY_ID" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Test document creation
curl -X POST "http://localhost:5000/api/onlyoffice/documents/POLICY_ID/create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"title": "Test Document", "type": "docx"}'
```

## Future Enhancements

### Planned Features
- [ ] Multi-user collaborative editing
- [ ] Document comparison and merge
- [ ] Advanced template system
- [ ] Document approval workflow integration
- [ ] Real-time collaboration indicators
- [ ] Document export to multiple formats
- [ ] Integration with external document repositories

### Performance Optimizations
- [ ] Document caching strategies
- [ ] Lazy loading of OnlyOffice API
- [ ] Optimized file serving
- [ ] Background document processing

### Security Enhancements
- [ ] Enhanced JWT validation
- [ ] Document encryption at rest
- [ ] Audit logging for document access
- [ ] Advanced permission granularity

## Support and Maintenance

### Monitoring
- OnlyOffice container health checks
- Document server performance metrics
- File storage usage monitoring
- API endpoint response times

### Backup Strategy
- Regular backup of document files
- Database backup including version history
- OnlyOffice configuration backup
- Disaster recovery procedures

### Updates and Patches
- OnlyOffice Document Server updates
- Security patch management
- Dependency updates
- Configuration migration procedures

---

For additional support or questions about the OnlyOffice integration, please refer to:
- [OnlyOffice Developer Documentation](https://api.onlyoffice.com/)
- [OnlyOffice Document Server GitHub](https://github.com/ONLYOFFICE/DocumentServer)
- Internal development team documentation
