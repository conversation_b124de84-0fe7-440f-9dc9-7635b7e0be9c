# Stage 1: Build the Next.js application
FROM node:18-alpine AS builder
 
WORKDIR /app
 
# Copy the package.json and yarn.lock to install dependencies
COPY package.json yarn.lock ./
RUN yarn install
 
# Copy the rest of the application files and build the Next.js app
COPY . .
RUN yarn build
 
# Stage 2: Serve the Next.js application
FROM node:18-alpine
 
WORKDIR /app
 
# Copy the built files from the builder stage
COPY --from=builder /app ./
 
# Expose port 3000 to be used by Docker
EXPOSE 3000
 
# Start the Next.js application
CMD ["yarn", "start"]
 