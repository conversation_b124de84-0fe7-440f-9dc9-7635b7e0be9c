{"extends": ["next/core-web-vitals", "eslint:recommended", "plugin:@typescript-eslint/recommended", "prettier"], "plugins": ["prettier", "@typescript-eslint", "unused-imports", "import"], "parser": "@typescript-eslint/parser", "rules": {"no-unused-vars": "off", "@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "warn", "unused-imports/no-unused-imports": "error", "@typescript-eslint/no-var-requires": "off", "@next/next/no-img-element": "off", "unused-imports/no-unused-vars": ["warn", {"vars": "all", "varsIgnorePattern": "^_", "args": "after-used", "argsIgnorePattern": "^_"}], "prettier/prettier": ["error", {"endOfLine": "auto"}], "import/no-unresolved": "error", "import/named": "error", "import/default": "error", "import/namespace": "error", "no-case-declarations": "off"}, "root": true}