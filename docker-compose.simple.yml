services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: grc-frontend
    restart: unless-stopped
    ports:
      - "3000:3000"  # Changed from 80:3000
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://localhost:5000
    depends_on:
      - backend
    networks:
      - grc-network

  # Backend on port 5000
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: grc-backend
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - PORT=5000
      - MONGODB_URI=${MONGODB_URI}  # Atlas connection string
      - JWT_SECRET=${JWT_SECRET}
      - FRONTEND_URL=http://localhost:3000
      - ONLYOFFICE_SERVER_URL=http://localhost:8080
      - ONLYOFFICE_JWT_SECRET=${ONLYOFFICE_JWT_SECRET}
    volumes:
      - ./backend/uploads:/app/uploads
    networks:
      - grc-network

  # OnlyOffice on port 8080
  onlyoffice:
    image: onlyoffice/documentserver:latest
    container_name: grc-onlyoffice
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      - JWT_ENABLED=false
      - DOC_SERV_MAX_FILE_SIZE=50000000
      - ALLOW_PRIVATE_IP_ADDRESS=true
    volumes:
      - onlyoffice_data:/var/www/onlyoffice/Data
    networks:
      - grc-network

volumes:
  onlyoffice_data:

networks:
  grc-network:
    driver: bridge




