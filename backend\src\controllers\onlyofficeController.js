const jwt = require('jsonwebtoken');
const path = require('path');
const fs = require('fs-extra');
const { v4: uuidv4 } = require('uuid');
const Policy = require('../models/Policy');
const { createDocumentFromTemplate, getMimeType } = require('../utils/documentTemplates');

// Get OnlyOffice configuration for a document
const getConfig = async (req, res) => {
  try {
    const { policyId } = req.params;
    
    // Get policy with attachment
    const policy = await Policy.findById(policyId);
    
    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found'
      });
    }
    
    // Check if policy has an attachment
    if (!policy.attachments || policy.attachments.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'No document attached to this policy'
      });
    }
    
    const attachment = policy.attachments[0];
    const filename = attachment.filename;
    const fileExt = path.extname(attachment.originalName).substring(1);
    
    // Generate document key for tracking changes (must be simple string without special characters)
    const documentKey = `${policy._id}_${Date.now()}`;
    
    // Determine document type
    let documentType = 'word';
    if (['xlsx', 'xls'].includes(fileExt)) {
      documentType = 'cell';
    } else if (['pptx', 'ppt'].includes(fileExt)) {
      documentType = 'slide';
    }
    
    // Base URL for API endpoints
    // Use host.docker.internal for Docker containers to access host machine
    const baseUrl = process.env.ONLYOFFICE_CALLBACK_URL || 
      (process.env.NODE_ENV === 'production'
        ? `${req.protocol}://${req.get('host')}`
        : `http://host.docker.internal:${process.env.PORT || 5000}`);
    
    // OnlyOffice configuration
    const config = {
      document: {
        fileType: fileExt,
        key: documentKey,
        title: attachment.originalName,
        url: `${baseUrl}/api/onlyoffice/documents/${policyId}/content`,
        permissions: {
          edit: policy.status === 'Draft',
          download: true,
          review: true,
          print: true,
          comment: true
        }
      },
      documentType,
      editorConfig: {
        callbackUrl: `${baseUrl}/api/onlyoffice/callback/${policyId}`,
        lang: 'en',
        mode: policy.status === 'Draft' ? 'edit' : 'view',
        user: {
          id: req.user._id,
          name: req.user.name
        },
        customization: {
          autosave: true,
          forcesave: true,
          chat: false,
          comments: true,
          compactToolbar: false,
          feedback: false,
          help: true
        }
      },
      height: '100%',
      width: '100%'
    };
    
    // Add JWT token if enabled
    if (process.env.ONLYOFFICE_JWT_SECRET) {
      config.token = jwt.sign(config, process.env.ONLYOFFICE_JWT_SECRET);
    }
    
    res.json({
      success: true,
      data: config
    });
  } catch (error) {
    console.error('Error generating OnlyOffice config:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate editor configuration'
    });
  }
};

// Get document content for OnlyOffice
const getDocumentContent = async (req, res) => {
  try {
    const { policyId } = req.params;
    
    // Get policy with attachment
    const policy = await Policy.findById(policyId);
    
    if (!policy || !policy.attachments || policy.attachments.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Document not found'
      });
    }
    
    const attachment = policy.attachments[0];
    const filePath = path.join(__dirname, '../../uploads/policies', attachment.filename);
    
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: 'Document file not found'
      });
    }
    
    // Set appropriate headers
    res.setHeader('Content-Type', attachment.mimeType);
    res.setHeader('Content-Disposition', `inline; filename="${attachment.originalName}"`);
    
    // Stream the file
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);
  } catch (error) {
    console.error('Error getting document content:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to retrieve document content'
    });
  }
};

// Handle callback from OnlyOffice
const handleCallback = async (req, res) => {
  try {
    const { policyId } = req.params;
    const { status, url } = req.body;
    
    console.log('OnlyOffice callback received:', {
      policyId,
      status,
      url
    });
    
    // Get policy
    const policy = await Policy.findById(policyId);
    
    if (!policy || !policy.attachments || policy.attachments.length === 0) {
      console.error('Policy or attachment not found for callback:', policyId);
      return res.json({ error: 0 }); // OnlyOffice expects error: 0 for success
    }
    
    // Handle document saving (status 2)
    if (status === 2 && url) {
      try {
        const axios = require('axios');
        const attachment = policy.attachments[0];
        const filePath = path.join(__dirname, '../../uploads/policies', attachment.filename);
        
        // Download updated document
        const response = await axios.get(url, { responseType: 'stream' });
        const writeStream = fs.createWriteStream(filePath);
        response.data.pipe(writeStream);
        
        await new Promise((resolve, reject) => {
          writeStream.on('finish', resolve);
          writeStream.on('error', reject);
        });
        
        // Update attachment metadata
        const stats = await fs.stat(filePath);
        attachment.size = stats.size;
        attachment.uploadedAt = new Date();
        
        // Increment version if versioning is enabled
        if (!policy.version) policy.version = '1.0';
        if (!policy.versionNumber) policy.versionNumber = 1.0;
        
        policy.versionNumber = Math.round((policy.versionNumber + 0.1) * 10) / 10;
        policy.version = policy.versionNumber.toFixed(1);
        
        // Add to version history if it doesn't exist
        if (!policy.versionHistory) policy.versionHistory = [];
        
        policy.versionHistory.push({
          version: policy.version,
          modifiedBy: {
            id: req.body.users?.[0]?.id || 'system',
            name: req.body.users?.[0]?.name || 'System'
          },
          modifiedAt: new Date(),
          size: stats.size
        });
        
        await policy.save();
        
        console.log('Document saved successfully:', policyId);
      } catch (saveError) {
        console.error('Error saving document:', saveError);
        return res.json({ error: 1 });
      }
    }
    
    // OnlyOffice expects a JSON response with error: 0 for success
    res.json({ error: 0 });
  } catch (error) {
    console.error('Callback error:', error);
    res.json({ error: 1 });
  }
};

// Create new document
const createDocument = async (req, res) => {
  try {
    const { policyId } = req.params;
    const { title, type = 'docx' } = req.body;
    
    if (!title) {
      return res.status(400).json({
        success: false,
        message: 'Document title is required'
      });
    }
    
    // Get policy
    const policy = await Policy.findById(policyId);
    
    if (!policy) {
      return res.status(404).json({
        success: false,
        message: 'Policy not found'
      });
    }
    
    // Check if policy can be edited
    if (policy.status !== 'Draft' && policy.status !== 'Request Initiated') {
      return res.status(400).json({
        success: false,
        message: 'Policy must be in Draft status to create a document'
      });
    }
    
    // Generate unique filename
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const filename = `policy-${uniqueSuffix}.${type}`;
    const uploadDir = path.join(__dirname, '../../uploads/policies');
    const filePath = path.join(uploadDir, filename);
    
    // Ensure upload directory exists
    await fs.ensureDir(uploadDir);
    
    // Create document from template
    await createDocumentFromTemplate(filePath, type, title);
    
    // Get file stats
    const stats = await fs.stat(filePath);
    
    // Create attachment object
    const attachment = {
      filename,
      originalName: `${title}.${type}`,
      url: `/uploads/policies/${filename}`,
      size: stats.size,
      mimeType: getMimeType(type),
      uploadedAt: new Date()
    };
    
    // Replace existing attachment (only one policy document allowed)
    policy.attachments = [attachment];
    
    // Initialize version if not exists
    if (!policy.version) policy.version = '1.0';
    if (!policy.versionNumber) policy.versionNumber = 1.0;
    
    // Initialize version history if not exists
    if (!policy.versionHistory) policy.versionHistory = [];
    
    policy.versionHistory.push({
      version: policy.version,
      modifiedBy: {
        id: req.user._id,
        name: req.user.name
      },
      modifiedAt: new Date(),
      size: stats.size,
      action: 'created'
    });
    
    await policy.save();
    
    res.status(201).json({
      success: true,
      message: 'Document created successfully',
      data: {
        policyId: policy._id,
        attachment
      }
    });
  } catch (error) {
    console.error('Error creating document:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create document'
    });
  }
};

module.exports = {
  getConfig,
  getDocumentContent,
  handleCallback,
  createDocument
};
