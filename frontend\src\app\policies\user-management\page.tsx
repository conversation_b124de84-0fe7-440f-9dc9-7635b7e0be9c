'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ErrorDisplay } from '@/components/ui/error-display';
import { LoadingDisplay } from '@/components/ui/loading-display';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useUsers } from '@/hooks/useUsers';
import { UserManagementModal } from '@/components/UserManagementModal';
import { UserProfileModal } from '@/components/UserProfileModal';
import { DataTable } from './datatable';
import { columns } from './column';
import { Plus, Search, Users, UserCheck, UserX, Shield } from 'lucide-react';

// Golf Saudi departments
const DEPARTMENTS = [
  'PMO Advisory Investment Activation',
  'Development',
  'Legal',
  'Procurement',
  'Marketing',
  'Golf Excellence',
  'Risk Management',
  'HR',
  'Sales & Sponsorships',
  'Events',
  'Compliance',
  'Finance',
  'Federation Office',
  'Academies',
  'Governance',
  'Business Continuity Management',
  'Events Management',
  'Local Golf',
  'Tournaments',
  'Internal Audit',
  'IT',
  'Cybersecurity',
  'Admin & HSSE',
  'CEO Office',
  'Strategy',
  'Golf Operations',
];

const ROLES = [
  'Creator',
  'Reviewer',
  'Approver',
  'Publisher',
  'Viewer',
  'Admin',
  'Super Admin',
];

interface User {
  _id: string;
  name: string;
  email: string;
  role: string;
  department: string;
  subDepartment?: string;
  position?: string;
  isActive: boolean;
  lastLogin?: string;
  createdAt: string;
  reportingManager?: {
    _id: string;
    name: string;
  };
}

const UserManagement = () => {
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRole, setSelectedRole] = useState<string>('all');
  const [selectedDepartment, setSelectedDepartment] = useState<string>('all');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  const { toast } = useToast();
  const { user: currentUser } = useAuth();
  const {
    users,
    loading,
    error,
    fetchUsers,
    createUser,
    updateUser,
    toggleUserStatus,
  } = useUsers();

  // Fetch users on component mount
  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  // Filter users based on search and filters
  useEffect(() => {
    let filtered = users;

    if (searchTerm) {
      filtered = filtered.filter(
        (user) =>
          user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          user.email.toLowerCase().includes(searchTerm.toLowerCase()),
      );
    }

    if (selectedRole !== 'all') {
      filtered = filtered.filter((user) => user.role === selectedRole);
    }

    if (selectedDepartment !== 'all') {
      filtered = filtered.filter(
        (user) => user.department === selectedDepartment,
      );
    }

    if (selectedStatus !== 'all') {
      filtered = filtered.filter((user) =>
        selectedStatus === 'active' ? user.isActive : !user.isActive,
      );
    }

    setFilteredUsers(filtered);
  }, [users, searchTerm, selectedRole, selectedDepartment, selectedStatus]);

  const handleCreateUser = async (userData: any) => {
    const result = await createUser(userData);
    if (result) {
      setIsCreateModalOpen(false);
    }
  };

  const handleEditUser = async (userData: any) => {
    if (selectedUser) {
      const result = await updateUser(selectedUser._id, userData);
      if (result) {
        setIsEditModalOpen(false);
        setSelectedUser(null);
      }
    }
  };

  const handleToggleUserStatus = async (userId: string) => {
    await toggleUserStatus(userId);
  };

  // Handle loading state
  if (loading) {
    return (
      <LoadingDisplay
        message="Loading user data..."
        variant="default"
        size="md"
      />
    );
  }

  // Handle error state
  if (error) {
    return (
      <div className="bg-gray-100 py-2">
        <ErrorDisplay
          variant="minimal"
          title="Unable to Load Users"
          message="We couldn't load the user data. Please try again or contact support if the problem persists."
          error={error || undefined}
          onRetry={() => fetchUsers()}
          showTechnicalDetails={true}
        />
      </div>
    );
  }

  // Statistics
  const stats = {
    total: users.length,
    active: users.filter((u) => u.isActive).length,
    inactive: users.filter((u) => !u.isActive).length,
    admins: users.filter((u) => ['Admin', 'Super Admin'].includes(u.role))
      .length,
  };

  return (
    <div className="bg-gray-100 py-2">
      <div className="mb-4 flex items-center justify-between">
        <h1 className="text-2xl font-bold text-customBlue">User Management</h1>
        <Button
          onClick={() => setIsCreateModalOpen(true)}
          className="bg-customBlue hover:bg-customBlueHover"
        >
          <Plus className="mr-2 h-4 w-4" />
          Add User
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="mb-6 grid grid-cols-1 gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
            <UserCheck className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {stats.active}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Inactive Users
            </CardTitle>
            <UserX className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {stats.inactive}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Administrators
            </CardTitle>
            <Shield className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {stats.admins}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-4">
            <div className="min-w-[200px] flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search users..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            <Select value={selectedRole} onValueChange={setSelectedRole}>
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="All Roles" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Roles</SelectItem>
                {ROLES.map((role) => (
                  <SelectItem key={role} value={role}>
                    {role}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select
              value={selectedDepartment}
              onValueChange={setSelectedDepartment}
            >
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="All Departments" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Departments</SelectItem>
                {DEPARTMENTS.map((dept) => (
                  <SelectItem key={dept} value={dept}>
                    {dept}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger className="w-[120px]">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Users DataTable */}
      <div className="mt-4">
        <DataTable
          columns={columns}
          data={filteredUsers}
          loading={loading}
          onEdit={(user: User) => {
            setSelectedUser(user);
            setIsEditModalOpen(true);
          }}
          onProfile={(user: User) => {
            setSelectedUser(user);
            setIsProfileModalOpen(true);
          }}
          onToggleStatus={handleToggleUserStatus}
        />
      </div>

      {/* Modals */}
      <UserManagementModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateUser}
        mode="create"
      />

      <UserManagementModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedUser(null);
        }}
        onSubmit={handleEditUser}
        mode="edit"
        user={selectedUser}
      />

      <UserProfileModal
        isOpen={isProfileModalOpen}
        onClose={() => {
          setIsProfileModalOpen(false);
          setSelectedUser(null);
        }}
        user={selectedUser}
      />
    </div>
  );
};

export default UserManagement;
