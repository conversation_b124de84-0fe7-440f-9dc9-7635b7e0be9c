# GRC Policy Management System - Complete Workflow Documentation

## 🎯 System Overview

The GRC Policy Management System is a comprehensive web application built with a modern tech stack to manage the complete lifecycle of organizational policies. The system implements a structured workflow with role-based access control, ensuring proper governance and compliance throughout the policy lifecycle.

### Architecture
- **Backend**: Node.js/Express API with MongoDB database
- **Frontend**: Next.js 14 with React TypeScript
- **Authentication**: JWT-based with role-based access control
- **Real-time**: Socket.IO for notifications
- **API**: RESTful endpoints with comprehensive validation

## 🏗️ System Components

### Backend Components
- **Server**: Express.js running on port 5000
- **Database**: MongoDB with comprehensive schemas
- **Authentication**: JWT middleware with role validation
- **File Storage**: Multer for document attachments
- **Real-time**: Socket.IO for live notifications
- **Validation**: Comprehensive input validation middleware

### Frontend Components
- **Framework**: Next.js 14 with App Router
- **UI**: React TypeScript with Tailwind CSS
- **State Management**: React Context + Custom Hooks
- **Data Tables**: TanStack Table for responsive data display
- **Forms**: React Hook Form with validation
- **Notifications**: Real-time toast notifications

## 👥 User Roles & Permissions

### Role Hierarchy
1. **Super Admin** - Full system access
2. **Admin** - Administrative privileges
3. **Creator** - Can create and manage own policies
4. **Approver** - Can approve policies (department-specific)
5. **Reviewer** - Can review policies (GRC department)
6. **Publisher** - Can publish approved policies
7. **Viewer** - Read-only access to published policies

### Department Structure
The system supports 23 departments including:
- Governance (handles policy approvals)
- GRC (handles policy reviews)
- HR, IT, Finance, Legal, Operations, etc.

### Permission Matrix
```
Role          | Create | Edit | Review | Approve | Publish | View All
------------- |--------|------|--------|---------|---------|----------
Super Admin   |   ✓    |  ✓   |   ✓    |    ✓    |    ✓    |    ✓
Admin         |   ✓    |  ✓   |   ✓    |    ✓    |    ✓    |    ✓
Creator       |   ✓    |  ✓   |   ✗    |    ✗    |    ✗    |    ✗
Approver      |   ✗    |  ✗   |   ✗    |    ✓    |    ✗    |    ✓
Reviewer      |   ✗    |  ✗   |   ✓    |    ✗    |    ✗    |    ✓
Publisher     |   ✗    |  ✗   |   ✗    |    ✗    |    ✓    |    ✓
Viewer        |   ✗    |  ✗   |   ✗    |    ✗    |    ✗    |    ✗
```

## 🔄 Complete Policy Workflow

### 1. Request Initiated
- **Actors**: Creators, Admins, Super Admins
- **Action**: Submit new policy request
- **API Endpoint**: `POST /api/policies/request`
- **Required Fields**: name, description, department, categories, priority
- **Next Status**: Request Initiated → Draft (via Governance Review)

### 2. Governance Review
- **Actors**: Approvers from Governance department, Admins, Super Admins
- **Action**: Review and approve/reject policy request
- **API Endpoint**: `POST /api/policies/:id/governance-review`
- **Business Rule**: Only Governance department Approvers can perform this action
- **Next Status**: Draft (if approved) or remains Request Initiated (if rejected)

### 3. Draft Status
- **Actors**: Policy Creators (owners)
- **Action**: Create policy content, upload documents
- **Features**: Document editing, attachment upload, content development
- **Next Status**: Draft → Under Review (via Submit for Review)

### 4. Submit for Review
- **Actors**: Policy Creators, Admins, Super Admins
- **Action**: Submit completed draft for GRC review
- **API Endpoint**: `POST /api/policies/:id/submit-for-review`
- **Next Status**: Under Review

### 5. GRC Review
- **Actors**: Reviewers from GRC department, Admins, Super Admins
- **Action**: Review policy for compliance and standards
- **API Endpoint**: `POST /api/policies/:id/grc-review`
- **Business Rule**: Only GRC department Reviewers can perform this action
- **Next Status**: Pending Approval (if approved) or Draft (if rejected)

### 6. Pending Approval
- **Actors**: Based on Policy Category
  - **Corporate Policies**: Board Committee approval required
  - **Operational Policies**: CEO approval required
- **Action**: Final approval decision
- **API Endpoint**: `POST /api/policies/:id/approve`
- **Next Status**: Approved (if approved) or Draft (if rejected)

### 7. Approved Status
- **System Status**: Policy ready for publication
- **Actors**: Publishers, Admins, Super Admins
- **Action**: Publish policy to repository
- **Next Status**: Published

### 8. Published Status
- **Actors**: Publishers, Admins, Super Admins
- **Action**: Policy is live and accessible to all users
- **API Endpoint**: `PUT /api/policies/:id/status`
- **Next Status**: Under Annual Review (scheduled) or Archived

## 📊 API Endpoints

### Authentication Endpoints
```
POST /api/auth/login          - User login
POST /api/auth/logout         - User logout
GET  /api/auth/me            - Get current user info
PUT  /api/auth/profile       - Update user profile
```

### Policy Management Endpoints
```
GET    /api/policies                    - Get all policies (with filtering)
GET    /api/policies/:id               - Get specific policy
POST   /api/policies/request           - Request policy initiation
PUT    /api/policies/:id               - Update policy
DELETE /api/policies/:id               - Delete policy
GET    /api/policies/stats             - Get policy statistics
GET    /api/policies/:id/history       - Get policy audit trail
```

### Workflow Action Endpoints
```
POST /api/policies/:id/governance-review  - Governance review action
POST /api/policies/:id/grc-review        - GRC review action
POST /api/policies/:id/submit-for-review - Submit for review action
POST /api/policies/:id/approve           - Approve policy action
POST /api/policies/:id/reject            - Reject policy action
PUT  /api/policies/:id/status           - Update policy status
```

### User Management Endpoints
```
GET    /api/users           - Get all users (Admin only)
GET    /api/users/:id       - Get specific user (Admin only)
POST   /api/users           - Create new user (Admin only)
PUT    /api/users/:id       - Update user (Admin only)
DELETE /api/users/:id       - Delete user (Admin only)
```

## 🗄️ Database Schema

### Policy Schema
```javascript
{
  policyId: String (unique),           // Auto-generated ID
  name: String (required),             // Policy name
  description: String,                 // Policy description
  documentName: String,                // Document name
  documentCode: String (unique),       // Document code
  documentType: Enum,                  // Policy, SoW, Framework, etc.
  version: String,                     // Version number
  department: String (required),       // Owning department
  subDepartment: String,              // Sub-department
  policyCategory: Enum,               // Corporate/Operational
  policyType: Enum,                   // Corporate, Technical, etc.
  categories: [String],               // Policy categories
  classification: Enum,               // Public, Internal, Confidential
  approvalAuthority: Enum,            // Board Committee, CEO, Dept Head
  priorityScore: Number,              // 1-10 priority score
  priority: Enum,                     // Low, Medium, High, Critical
  status: Enum,                       // Workflow status
  policyOwner: Object,                // Owner details
  initiatedBy: Object,                // Initiator details
  workflowHistory: [Object],          // Audit trail
  governanceReview: Object,           // Governance review details
  grcReview: Object,                  // GRC review details
  approvalWorkflow: [Object],         // Approval history
  attachments: [Object],              // File attachments
  dates: Object,                      // Various dates
  acknowledgements: [Object]          // User acknowledgements
}
```

### User Schema
```javascript
{
  name: String (required),            // Full name
  email: String (unique, required),   // Email address
  password: String (required),        // Hashed password
  role: Enum (required),             // User role
  department: Enum (required),       // Department
  subDepartment: String,             // Sub-department
  position: String,                  // Job position
  reportingManager: ObjectId,        // Manager reference
  permissions: [String],             // Specific permissions
  isActive: Boolean,                 // Account status
  lastLogin: Date,                   // Last login timestamp
  profileImage: String,              // Profile image URL
  phoneNumber: String,               // Contact number
  address: Object,                   // Address details
  preferences: Object                // User preferences
}
```

## 🎯 My Actions Feature

The "My Actions" page is a personalized dashboard that shows users only the policies requiring their attention based on their role and current policy status.

### Action Categories

#### 1. Urgent Actions
- Policies with approaching deadlines
- High-priority items requiring immediate attention
- Overdue reviews or approvals

#### 2. My Drafts
- **For Creators**: Policies they own in Draft status
- **For Admins**: All policies in Draft status
- Action: Continue editing, submit for review

#### 3. Awaiting My Review
- **For GRC Reviewers**: Policies in "Under Review" status
- **For Governance Approvers**: Policies in "Request Initiated" status
- Action: Review and approve/reject

#### 4. Awaiting My Approval
- **For Approvers**: Policies in "Pending Approval" status
- Based on policy category and approval authority
- Action: Final approval decision

#### 5. Ready to Publish
- **For Publishers**: Policies in "Approved" status
- Action: Publish to make live

#### 6. Annual Review
- **For Policy Owners**: Policies in "Under Annual Review" status
- Action: Review and update as needed

### Role-Based Filtering Logic
```javascript
// My Actions filtering based on user role
switch (userRole) {
  case 'Creator':
    // Only see policies they own
    query['policyOwner.id'] = userId;
    break;
    
  case 'Reviewer':
    // See policies needing GRC review + own policies
    // Department must be GRC for review actions
    break;
    
  case 'Approver':
    // See policies needing governance review + approval
    // Department-specific for governance review
    break;
    
  case 'Publisher':
    // See approved policies ready for publishing
    break;
    
  case 'Admin':
  case 'Super Admin':
    // See all policies for management
    break;
    
  case 'Viewer':
    // Only see published policies (read-only)
    break;
}
```

## 🔐 Authentication & Security

### JWT Authentication
- **Token Storage**: localStorage on frontend
- **Token Validation**: Middleware on all protected routes
- **Token Refresh**: Automatic refresh on API calls
- **Session Management**: Secure logout with token cleanup

### Security Features
- **Password Hashing**: bcrypt with salt rounds
- **Input Validation**: Comprehensive validation middleware
- **SQL Injection Protection**: MongoDB native protection
- **XSS Protection**: Input sanitization
- **CORS Configuration**: Restricted origins
- **Rate Limiting**: API rate limiting middleware

### Authorization Levels
1. **Public Routes**: Policy statistics, health check
2. **Protected Routes**: Require valid JWT token
3. **Role-Based Routes**: Additional role validation
4. **Permission-Based Routes**: Specific permission checks
5. **Ownership-Based Routes**: Resource ownership validation

## 📱 Frontend Implementation

### Page Structure
```
/policies/
├── manage/          - Policy management dashboard
├── my-actions/      - Personalized action dashboard
├── policyhub/       - Published policy repository
├── editor/[id]/     - Policy document editor
└── create/          - New policy creation form
```

### Key Components

#### 1. My Actions Page (`/policies/my-actions/page.tsx`)
- **Purpose**: Personalized dashboard for user-specific actions
- **Features**:
  - Role-based policy filtering
  - Action categorization (urgent, drafts, reviews, approvals)
  - Real-time updates
  - Quick action buttons
- **Hooks**: `useMyActions`, `usePolicies`, `useAuth`

#### 2. Policy Management (`/policies/manage/`)
- **Purpose**: Administrative policy management
- **Features**:
  - Comprehensive policy listing
  - Advanced filtering and search
  - Bulk operations
  - Status management
- **Components**: DataTable with TanStack Table

#### 3. Policy Hub (`/policies/policyhub/`)
- **Purpose**: Published policy repository
- **Features**:
  - Public policy access
  - Category browsing
  - Search functionality
  - Document download

#### 4. Policy Editor (`/policies/editor/[id]/`)
- **Purpose**: Document editing interface
- **Features**:
  - Rich text editing
  - Document collaboration
  - Version control
  - Auto-save functionality

### Custom Hooks

#### `useMyActions`
```typescript
interface UseMyActionsReturn {
  actionablePolicies: Policy[];
  actionCategories: ActionCategories;
  totalActionCount: number;
  hasUrgentActions: boolean;
}
```
- **Purpose**: Categorizes policies based on user role and status
- **Logic**: Implements complex role-based filtering
- **Categories**: Urgent, drafts, reviews, approvals, publishing, annual review

#### `usePolicies`
```typescript
interface UsePoliciesReturn {
  policies: Policy[];
  metadata: PaginationMetadata;
  loading: boolean;
  error: string | null;
  refetch: () => void;
  // Action methods
  updatePolicyStatus: (id, status, comments) => Promise<boolean>;
  governanceReview: (id, decision, comments) => Promise<boolean>;
  grcReview: (id, decision, comments) => Promise<boolean>;
  // ... other workflow actions
}
```
- **Purpose**: Comprehensive policy data management
- **Features**: CRUD operations, workflow actions, real-time updates

#### `useAuth`
```typescript
interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email, password) => Promise<boolean>;
  logout: () => Promise<void>;
  isAuthenticated: boolean;
}
```
- **Purpose**: Authentication state management
- **Features**: Login/logout, token management, user context

## 🔔 Notification System

### Real-time Notifications
- **Technology**: Socket.IO for real-time communication
- **Authentication**: JWT-based socket authentication
- **Events**: Policy status changes, assignments, deadlines

### Notification Types
1. **Policy Created**: New policy request submitted
2. **Review Required**: Policy needs review/approval
3. **Status Changed**: Policy status updated
4. **Deadline Approaching**: Due date reminders
5. **Policy Published**: New policy available
6. **Assignment**: Policy assigned to user

### Notification Delivery
- **In-App**: Real-time toast notifications
- **Email**: Scheduled email notifications
- **Dashboard**: Notification bell with count
- **My Actions**: Integrated action items

## 📊 Status Transitions & Business Rules

### Valid Status Transitions
```
Request Initiated → Draft (via Governance Review)
Draft → Under Review (via Submit for Review)
Under Review → Pending Approval (via GRC Review - Approved)
Under Review → Draft (via GRC Review - Rejected)
Pending Approval → Approved (via Final Approval)
Pending Approval → Draft (via Rejection)
Approved → Published (via Publishing)
Approved → Archived (via Archival)
Published → Under Annual Review (scheduled)
Published → Archived (via Archival)
Under Annual Review → Approved (via Review Completion)
Under Annual Review → Draft (via Major Changes)
Archived → Draft (via Reactivation)
```

### Business Rules

#### Policy Category Rules
- **Corporate Policies**: Require Board Committee approval
- **Operational Policies**: Require CEO approval
- **Auto-Assignment**: Approval authority set based on category

#### Department-Specific Rules
- **Governance Review**: Only Governance department Approvers
- **GRC Review**: Only GRC department Reviewers
- **Publishing**: Publishers, Admins, Super Admins only

#### Priority & Urgency Rules
- **High Priority**: Policies with priority score 8-10
- **Urgent Actions**: Policies with due dates within 7 days
- **Overdue**: Policies past due date

#### Version Control Rules
- **Major Changes**: Increment version number (1.0 → 2.0)
- **Minor Changes**: Increment decimal (1.0 → 1.1)
- **Auto-Versioning**: System tracks version history



## 🚀 Deployment & Configuration

### Environment Variables
```bash
# Backend (.env)
NODE_ENV=production
PORT=5000
MONGODB_URI=mongodb://localhost:27017/grc-policy-db
JWT_SECRET=your-jwt-secret
JWT_EXPIRE=30d

# Frontend (.env.local)
NEXT_PUBLIC_API_URL=http://localhost:5000
NEXT_PUBLIC_SOCKET_URL=http://localhost:5000
```

### Production Deployment
1. **Backend**: Node.js server with PM2 process manager
2. **Frontend**: Next.js static export or Vercel deployment
3. **Database**: MongoDB Atlas or self-hosted MongoDB
4. **File Storage**: Local storage or cloud storage integration
5. **SSL**: HTTPS configuration for production

### Performance Optimizations
- **Database Indexing**: Optimized queries with proper indexes
- **Caching**: Redis caching for frequently accessed data
- **Pagination**: Efficient data loading with pagination
- **Lazy Loading**: Component-level lazy loading
- **Code Splitting**: Route-based code splitting

## 📈 Monitoring & Analytics

### System Monitoring
- **Health Checks**: `/health` endpoint for system status
- **Error Logging**: Comprehensive error tracking
- **Performance Metrics**: API response time monitoring
- **User Activity**: Login/logout tracking

### Business Analytics
- **Policy Statistics**: Creation, approval, publishing metrics
- **User Engagement**: Action completion rates
- **Workflow Efficiency**: Time-to-completion tracking
- **Compliance Metrics**: Policy adherence reporting

## 🔧 Maintenance & Support

### Regular Maintenance Tasks
1. **Database Cleanup**: Archive old policy versions
2. **User Management**: Deactivate inactive users
3. **Performance Tuning**: Optimize slow queries
4. **Security Updates**: Regular dependency updates
5. **Backup Management**: Automated database backups

### Support Features
- **Audit Trail**: Complete action history
- **Error Recovery**: Graceful error handling
- **Data Export**: Policy and user data export
- **System Logs**: Comprehensive logging system

---

## 📞 Support & Contact

For technical support or questions about the GRC Policy Management System:
- **Documentation**: This comprehensive guide
- **API Reference**: Backend API documentation
- **Error Logs**: Check application logs for troubleshooting

---

*This documentation covers the complete implementation of the GRC Policy Management System workflow, including all technical details, business rules, and operational procedures.*
