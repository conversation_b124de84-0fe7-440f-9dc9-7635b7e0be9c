const Notification = require('../models/Notification');
const User = require('../models/User');

class NotificationService {
  constructor(io = null) {
    this.io = io; // Socket.IO instance for real-time notifications
  }

  // Set Socket.IO instance
  setSocketIO(io) {
    this.io = io;
  }

  // Create and send notification
  async createNotification(notificationData) {
    try {
      const notification = await Notification.createNotification(notificationData);
      
      // Send real-time notification if WebSocket is available
      if (this.io) {
        this.io.to(`user_${notification.recipient.id}`).emit('new_notification', notification);
      }

      return notification;
    } catch (error) {
      console.error('Failed to create notification:', error);
      throw error;
    }
  }

  // Send notification to multiple users
  async createBulkNotifications(recipients, notificationTemplate) {
    try {
      const notifications = [];
      
      for (const recipient of recipients) {
        const notificationData = {
          ...notificationTemplate,
          recipient: {
            id: recipient._id,
            email: recipient.email,
            role: recipient.role,
          },
        };
        
        const notification = await this.createNotification(notificationData);
        notifications.push(notification);
      }

      return notifications;
    } catch (error) {
      console.error('Failed to create bulk notifications:', error);
      throw error;
    }
  }

  // Policy workflow notification handlers
  async notifyPolicyRequestInitiated(policy, initiator) {
    try {
      // Find governance approvers (Approvers from Governance department + Admins + Super Admins)
      const governanceApprovers = await User.find({
        $or: [
          { role: 'Approver', department: 'Governance', isActive: true },
          { role: { $in: ['Admin', 'Super Admin'] }, isActive: true }
        ]
      });

      const notificationTemplate = {
        type: 'policy_workflow',
        category: 'approval_required',
        title: 'New Policy Request for Review',
        message: `${policy.name} has been submitted for governance review`,
        priority: 'high',
        data: {
          policyId: policy._id,
          policyName: policy.name,
          actionRequired: true,
          actionUrl: `/policies/manage/${policy._id}`,
        },
        sender: {
          id: initiator._id,
          name: initiator.name,
          email: initiator.email,
        },
      };

      return await this.createBulkNotifications(governanceApprovers, notificationTemplate);
    } catch (error) {
      console.error('Failed to notify policy request initiated:', error);
    }
  }

  async notifyGovernanceReview(policy, reviewer, decision, policyOwner) {
    try {
      const isApproved = decision === 'Approved';
      
      const notificationData = {
        recipient: {
          id: policyOwner._id,
          email: policyOwner.email,
          role: policyOwner.role,
        },
        type: 'policy_workflow',
        category: 'status_change',
        title: `Policy Request ${decision}`,
        message: `Your policy request "${policy.name}" has been ${decision.toLowerCase()} by governance review`,
        priority: isApproved ? 'medium' : 'high',
        data: {
          policyId: policy._id,
          policyName: policy.name,
          previousStatus: 'Request Initiated',
          newStatus: isApproved ? 'Draft' : 'Request Initiated',
          actionRequired: isApproved,
          actionUrl: `/policies/manage/${policy._id}`,
        },
        sender: {
          id: reviewer._id,
          name: reviewer.name,
          email: reviewer.email,
        },
      };

      return await this.createNotification(notificationData);
    } catch (error) {
      console.error('Failed to notify governance review:', error);
    }
  }

  async notifyGRCReview(policy, reviewer, decision, policyOwner) {
    try {
      const isApproved = decision === 'Approved';
      
      if (isApproved) {
        // Notify approvers (CEO/Board Committee based on policy category)
        const approvers = await this.getApproversForPolicy(policy);
        
        const approverNotificationTemplate = {
          type: 'policy_workflow',
          category: 'approval_required',
          title: 'Policy Pending Final Approval',
          message: `${policy.name} has passed GRC review and requires final approval`,
          priority: 'high',
          data: {
            policyId: policy._id,
            policyName: policy.name,
            actionRequired: true,
            actionUrl: `/policies/manage/${policy._id}`,
          },
          sender: {
            id: reviewer._id,
            name: reviewer.name,
            email: reviewer.email,
          },
        };

        await this.createBulkNotifications(approvers, approverNotificationTemplate);
      }

      // Notify policy owner
      const ownerNotificationData = {
        recipient: {
          id: policyOwner._id,
          email: policyOwner.email,
          role: policyOwner.role,
        },
        type: 'policy_workflow',
        category: 'status_change',
        title: `GRC Review ${decision}`,
        message: `Your policy "${policy.name}" has been ${decision.toLowerCase()} by GRC review`,
        priority: isApproved ? 'medium' : 'high',
        data: {
          policyId: policy._id,
          policyName: policy.name,
          previousStatus: 'Under Review',
          newStatus: isApproved ? 'Pending Approval' : 'Draft',
          actionRequired: !isApproved,
          actionUrl: `/policies/manage/${policy._id}`,
        },
        sender: {
          id: reviewer._id,
          name: reviewer.name,
          email: reviewer.email,
        },
      };

      return await this.createNotification(ownerNotificationData);
    } catch (error) {
      console.error('Failed to notify GRC review:', error);
    }
  }

  async notifyPolicyApproval(policy, approver, policyOwner) {
    try {
      // Notify policy owner
      const ownerNotification = await this.createNotification({
        recipient: {
          id: policyOwner._id,
          email: policyOwner.email,
          role: policyOwner.role,
        },
        type: 'policy_workflow',
        category: 'status_change',
        title: 'Policy Approved',
        message: `Your policy "${policy.name}" has been approved and is ready for publication`,
        priority: 'medium',
        data: {
          policyId: policy._id,
          policyName: policy.name,
          previousStatus: 'Pending Approval',
          newStatus: 'Approved',
          actionRequired: false,
          actionUrl: `/policies/manage/${policy._id}`,
        },
        sender: {
          id: approver._id,
          name: approver.name,
          email: approver.email,
        },
      });

      // Notify publishers
      const publishers = await User.find({
        role: { $in: ['Publisher', 'Admin', 'Super Admin'] },
        isActive: true,
      });

      const publisherNotificationTemplate = {
        type: 'policy_workflow',
        category: 'assignment',
        title: 'Policy Ready for Publication',
        message: `${policy.name} has been approved and is ready for publication`,
        priority: 'medium',
        data: {
          policyId: policy._id,
          policyName: policy.name,
          actionRequired: true,
          actionUrl: `/policies/manage/${policy._id}`,
        },
        sender: {
          id: approver._id,
          name: approver.name,
          email: approver.email,
        },
      };

      await this.createBulkNotifications(publishers, publisherNotificationTemplate);

      return ownerNotification;
    } catch (error) {
      console.error('Failed to notify policy approval:', error);
    }
  }

  async notifyPolicyPublished(policy, publisher) {
    try {
      // Notify policy owner
      const policyOwner = await User.findById(policy.policyOwner.id);
      
      if (policyOwner) {
        await this.createNotification({
          recipient: {
            id: policyOwner._id,
            email: policyOwner.email,
            role: policyOwner.role,
          },
          type: 'policy_workflow',
          category: 'status_change',
          title: 'Policy Published',
          message: `Your policy "${policy.name}" has been successfully published`,
          priority: 'low',
          data: {
            policyId: policy._id,
            policyName: policy.name,
            previousStatus: 'Approved',
            newStatus: 'Published',
            actionRequired: false,
            actionUrl: `/policies/manage/${policy._id}`,
          },
          sender: {
            id: publisher._id,
            name: publisher.name,
            email: publisher.email,
          },
        });
      }

      // Notify relevant department users
      const departmentUsers = await User.find({
        department: policy.department,
        isActive: true,
        _id: { $ne: policy.policyOwner.id }, // Exclude policy owner (already notified)
      });

      const departmentNotificationTemplate = {
        type: 'policy_workflow',
        category: 'status_change',
        title: 'New Policy Published',
        message: `${policy.name} has been published and is now effective`,
        priority: 'low',
        data: {
          policyId: policy._id,
          policyName: policy.name,
          actionRequired: false,
          actionUrl: `/policies/view/${policy._id}`,
        },
        sender: {
          id: publisher._id,
          name: publisher.name,
          email: publisher.email,
        },
      };

      return await this.createBulkNotifications(departmentUsers, departmentNotificationTemplate);
    } catch (error) {
      console.error('Failed to notify policy published:', error);
    }
  }

  // Helper method to get approvers based on policy category
  async getApproversForPolicy(policy) {
    let roleFilter;
    
    if (policy.policyCategory === 'Corporate Policies') {
      // Board Committee level approval
      roleFilter = { role: { $in: ['Admin', 'Super Admin'] } };
    } else if (policy.policyCategory === 'Operational Policies') {
      // CEO level approval
      roleFilter = { role: { $in: ['Approver', 'Admin', 'Super Admin'] } };
    } else {
      // Department Head level approval
      roleFilter = { 
        role: { $in: ['Approver', 'Admin', 'Super Admin'] },
        department: policy.department,
      };
    }

    return await User.find({ ...roleFilter, isActive: true });
  }

  // Due date reminder notifications
  async sendDueDateReminders() {
    try {
      const Policy = require('../models/Policy');

      // Find policies with due dates in the next 3 days
      const threeDaysFromNow = new Date();
      threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);

      const policiesDueSoon = await Policy.find({
        dueDate: { $lte: threeDaysFromNow, $gte: new Date() },
        status: { $in: ['Draft', 'Under Review', 'Pending Approval'] },
      }).populate('policyOwner.id');

      for (const policy of policiesDueSoon) {
        if (policy.policyOwner?.id) {
          await this.createNotification({
            recipient: {
              id: policy.policyOwner.id._id,
              email: policy.policyOwner.id.email,
              role: policy.policyOwner.id.role,
            },
            type: 'reminder',
            category: 'due_date',
            title: 'Policy Due Date Approaching',
            message: `Policy "${policy.name}" is due in ${Math.ceil((policy.dueDate - new Date()) / (1000 * 60 * 60 * 24))} days`,
            priority: 'medium',
            data: {
              policyId: policy._id,
              policyName: policy.name,
              dueDate: policy.dueDate,
              actionRequired: true,
              actionUrl: `/policies/manage/${policy._id}`,
            },
          });
        }
      }
    } catch (error) {
      console.error('Failed to send due date reminders:', error);
    }
  }

  // Start scheduled tasks
  startScheduledTasks() {
    // Send due date reminders every day at 9 AM
    const scheduleReminders = () => {
      const now = new Date();
      const scheduledTime = new Date();
      scheduledTime.setHours(9, 0, 0, 0); // 9:00 AM

      // If it's already past 9 AM today, schedule for tomorrow
      if (now > scheduledTime) {
        scheduledTime.setDate(scheduledTime.getDate() + 1);
      }

      const timeUntilScheduled = scheduledTime.getTime() - now.getTime();

      setTimeout(() => {
        this.sendDueDateReminders();
        // Schedule the next reminder (24 hours later)
        setInterval(() => {
          this.sendDueDateReminders();
        }, 24 * 60 * 60 * 1000); // 24 hours
      }, timeUntilScheduled);
    };

    scheduleReminders();
    console.log('📅 Due date reminder scheduler started');
  }
}

module.exports = NotificationService;
