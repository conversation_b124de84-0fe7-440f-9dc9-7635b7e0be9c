// API configuration and service layer for the GRC web app

const API_BASE_URL = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'}/api`;

// Types for API responses
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface PolicyMetadata {
  // Filtered counts (matching current query/filters)
  totalPolicies: number;
  statusCounts: {
    requestInitiated: number;
    draft: number;
    underReview: number;
    pendingApproval: number;
    approved: number;
    published: number;
    archived: number;
    underAnnualReview: number;
  };
  statusBreakdown: Record<string, number>;

  // Total counts (all policies, for dashboard overview)
  totalPoliciesCount: number;
  totalStatusCounts: {
    requestInitiated: number;
    draft: number;
    underReview: number;
    pendingApproval: number;
    approved: number;
    published: number;
    archived: number;
    underAnnualReview: number;
  };
  totalStatusBreakdown: Record<string, number>;
}

export interface User {
  _id: string;
  name: string;
  email: string;
  role: string;
  department: string;
  status?: 'Active' | 'Inactive';
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
}

export interface PolicyUser {
  id: string;
  name: string;
  email: string;
  department?: string;
  role?: string;
}

export interface PolicyReviewer extends PolicyUser {
  reviewDate?: string;
  comments?: string;
}

export interface PolicyEndorser extends PolicyUser {
  endorsementDate?: string;
  comments?: string;
}

export interface WorkflowHistoryEntry {
  status: string;
  timestamp: string;
  actor?: {
    id?: string;
    name?: string;
    email?: string;
    role?: string;
  };
  comments?: string;
  action?:
    | 'Created'
    | 'Submitted'
    | 'Approved'
    | 'Rejected'
    | 'Published'
    | 'Archived';
}

export interface ApprovalWorkflowEntry {
  step: number;
  approver: {
    id: string;
    name: string;
    email: string;
  };
  status: 'Pending' | 'Approved' | 'Rejected';
  comments?: string;
  actionDate?: string;
}

export interface GovernanceReview {
  reviewer?: {
    id?: string;
    name?: string;
    email?: string;
  };
  reviewDate?: string;
  decision?: 'Approved' | 'Rejected';
  comments?: string;
}

export interface GrcReview {
  reviewer?: {
    id?: string;
    name?: string;
    email?: string;
  };
  reviewDate?: string;
  decision?: 'Approved' | 'Rejected';
  comments?: string;
}

export interface Policy {
  _id: string;
  policyId: string;
  name: string;
  description?: string;

  // Document Information
  documentName?: string;
  documentCode?: string;
  documentType:
    | 'Policy'
    | 'SoW'
    | 'Framework'
    | 'Procedure'
    | 'Guideline'
    | 'Standard';
  version: string;
  versionNumber: number;

  // Department Information
  department: string;
  subDepartment?: string;

  // Policy Classification
  policyCategory: 'Corporate Policies' | 'Operational Policies';
  policyType:
    | 'Corporate'
    | 'Operational'
    | 'Technical'
    | 'Administrative'
    | 'Strategic';
  categories: string[];
  classification:
    | 'Public'
    | 'Internal'
    | 'Confidential'
    | 'Restricted'
    | 'Top Secret';
  approvalAuthority: 'Board Committee' | 'CEO' | 'Department Head';

  // Priority and Scoring
  priorityScore: number;

  // Ownership and Responsibility
  policyOwner: PolicyUser;
  initiatedBy?: PolicyUser;
  reviewedBy?: PolicyReviewer[];
  endorsedBy?: PolicyEndorser[];
  authorizedApprover?: PolicyUser;

  // Status Information
  status:
    | 'Request Initiated'
    | 'Draft'
    | 'Under Review'
    | 'Pending Approval'
    | 'Approved'
    | 'Published'
    | 'Archived'
    | 'Under Annual Review';
  detailedStatus?: string;

  // Content
  content?: string;

  // Attachments
  attachments?: {
    filename: string;
    originalName: string;
    url: string;
    size: number;
    mimeType: string;
    uploadedAt: string;
  }[];

  // Workflow and Review Data
  workflowHistory?: WorkflowHistoryEntry[];
  approvalWorkflow?: ApprovalWorkflowEntry[];
  governanceReview?: GovernanceReview;
  grcReview?: GrcReview;

  // Date Management
  startDate?: string;
  dueDate?: string;
  completionDate?: string;
  approvalDate?: string;
  lastReviewDate?: string;
  nextReviewDate?: string;
  effectiveDate?: string;
  publishedDate?: string;

  // Legacy metadata for backward compatibility
  metadata: {
    priority: 'Low' | 'Medium' | 'High' | 'Critical';
    dueDate?: string;
    tags?: string[];
    effectiveDate?: string;
    reviewDate?: string;
  };

  // Timestamps
  requestedAt: string;
  createdAt: string;
  updatedAt: string;
}

export interface PolicyStats {
  totalPolicies: number;
  approvedPolicies: number;
  pendingPolicies: number;
  rejectedPolicies: number;

  // Individual workflow status counts
  requestInitiated: number;
  draft: number;
  pendingApproval: number;
  approved: number;
  underAnnualReview: number;

  statusBreakdown: Record<string, number>;
}

export interface User {
  _id: string;
  name: string;
  email: string;
  role: string;
  department: string;
  isActive: boolean;
  lastLogin?: string;
  createdAt: string;
}

// API client class
class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    // Get token from localStorage if available
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('token');
    }
  }

  setToken(token: string) {
    this.token = token;
    if (typeof window !== 'undefined') {
      localStorage.setItem('token', token);
    }
  }

  removeToken() {
    this.token = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem('token');
    }
  }

  refreshToken() {
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('token');
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(options.headers as Record<string, string>),
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
      console.log(
        'Sending request with token:',
        this.token.substring(0, 20) + '...',
      );
    } else {
      console.log('No token available for request to:', endpoint);
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      const data = await response.json();

      if (!response.ok) {
        // Handle 401 Unauthorized responses
        if (response.status === 401) {
          this.removeToken();
          // Redirect to login if we're in the browser
          if (
            typeof window !== 'undefined' &&
            !window.location.pathname.includes('/login')
          ) {
            window.location.href = '/login';
          }
        }
        throw new Error(data.error || data.message || 'An error occurred');
      }

      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // Auth methods
  async login(email: string, password: string): Promise<ApiResponse<User>> {
    const response = await this.request<User>('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });

    // Backend returns { success: true, token, data: user }
    if (response.success && (response as any).token) {
      console.log(
        'Setting token from login response:',
        (response as any).token,
      );
      this.setToken((response as any).token);
    } else {
      console.error('No token in login response:', response);
    }

    return response;
  }

  async register(userData: {
    name: string;
    email: string;
    password: string;
    role?: string;
    department: string;
  }): Promise<ApiResponse<User>> {
    const response = await this.request<User>('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });

    // Backend returns { success: true, token, data: user }
    if (response.success && (response as any).token) {
      this.setToken((response as any).token);
    }

    return response;
  }

  async logout(): Promise<ApiResponse<null>> {
    const response = await this.request<null>('/auth/logout', {
      method: 'POST',
    });

    this.removeToken();
    return response;
  }

  async getCurrentUser(): Promise<ApiResponse<User>> {
    return this.request<User>('/auth/me');
  }

  // Policy methods
  async getPolicies(params?: {
    page?: number;
    limit?: number;
    status?: string;
    department?: string;
    search?: string;
    management?: boolean;
    myActions?: boolean;
  }): Promise<
    ApiResponse<{
      policies: Policy[];
      pagination: PaginationInfo;
      metadata: PolicyMetadata;
    }>
  > {
    const searchParams = new URLSearchParams();

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString());
        }
      });
    }

    const endpoint = `/policies${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    console.log('API endpoint being called:', endpoint);
    return this.request<{
      policies: Policy[];
      pagination: PaginationInfo;
      metadata: PolicyMetadata;
    }>(endpoint);
  }

  async getPolicyById(id: string): Promise<ApiResponse<Policy>> {
    return this.request<Policy>(`/policies/${id}`);
  }

  async requestPolicyInitiation(policyData: {
    name: string;
    description?: string;
    documentName?: string;
    documentCode?: string;
    documentType?: string;
    version?: string;
    policyCategory: 'Corporate Policies' | 'Operational Policies';
    policyType?: string;
    categories: string[];
    department: string;
    subDepartment?: string;
    priorityScore?: number;
    classification?: string;
    priority?: string;
    dueDate?: string;
    startDate?: string;
    nextReviewDate?: string;
  }): Promise<ApiResponse<Policy>> {
    return this.request<Policy>('/policies/request', {
      method: 'POST',
      body: JSON.stringify(policyData),
    });
  }

  async updatePolicy(
    id: string,
    policyData: Partial<Policy>,
  ): Promise<ApiResponse<Policy>> {
    return this.request<Policy>(`/policies/${id}`, {
      method: 'PUT',
      body: JSON.stringify(policyData),
    });
  }

  async deletePolicy(id: string): Promise<ApiResponse<null>> {
    return this.request<null>(`/policies/${id}`, {
      method: 'DELETE',
    });
  }

  async updatePolicyStatus(
    id: string,
    status: string,
    comments?: string,
  ): Promise<ApiResponse<Policy>> {
    return this.request<Policy>(`/policies/${id}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status, comments }),
    });
  }

  async approvePolicy(
    id: string,
    comments?: string,
  ): Promise<ApiResponse<Policy>> {
    return this.request<Policy>(`/policies/${id}/approve`, {
      method: 'POST',
      body: JSON.stringify({ comments }),
    });
  }

  async rejectPolicy(
    id: string,
    comments?: string,
  ): Promise<ApiResponse<Policy>> {
    return this.request<Policy>(`/policies/${id}/reject`, {
      method: 'POST',
      body: JSON.stringify({ comments }),
    });
  }

  async governanceReview(
    id: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ): Promise<ApiResponse<Policy>> {
    return this.request<Policy>(`/policies/${id}/governance-review`, {
      method: 'POST',
      body: JSON.stringify({ decision, comments }),
    });
  }

  async grcReview(
    id: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ): Promise<ApiResponse<Policy>> {
    return this.request<Policy>(`/policies/${id}/grc-review`, {
      method: 'POST',
      body: JSON.stringify({ decision, comments }),
    });
  }

  async submitPolicyForReview(
    id: string,
    comments?: string,
  ): Promise<ApiResponse<Policy>> {
    return this.request<Policy>(`/policies/${id}/submit-for-review`, {
      method: 'POST',
      body: JSON.stringify({ comments }),
    });
  }

  async getPolicyStats(): Promise<ApiResponse<PolicyStats>> {
    return this.request<PolicyStats>('/policies/stats');
  }

  async uploadPolicyAttachment(
    id: string,
    file: File,
  ): Promise<
    ApiResponse<{
      filename: string;
      originalName: string;
      url: string;
      size: number;
      mimeType: string;
      uploadedAt: string;
    }>
  > {
    const formData = new FormData();
    formData.append('file', file);

    // For file uploads, we need to handle the request differently
    const token = localStorage.getItem('token');
    const headers: Record<string, string> = {};

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    // Don't set Content-Type for FormData - let browser set it with boundary

    const response = await fetch(
      `${this.baseURL}/uploads/policies/${id}/attachments`,
      {
        method: 'POST',
        headers,
        body: formData,
      },
    );

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.message || `HTTP error! status: ${response.status}`);
    }

    return {
      success: data.success,
      data: data.data,
      message: data.message,
    };
  }

  // User Management Methods
  async getUsers(): Promise<ApiResponse<User[]>> {
    return this.request<User[]>('/users');
  }

  async getUserById(id: string): Promise<ApiResponse<User>> {
    return this.request<User>(`/users/${id}`);
  }

  async createUser(userData: {
    name: string;
    email: string;
    role: string;
    department: string;
    password: string;
  }): Promise<ApiResponse<User>> {
    return this.request<User>('/users', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  async updateUser(
    id: string,
    userData: {
      name: string;
      email: string;
      role: string;
      department: string;
      password?: string;
    },
  ): Promise<ApiResponse<User>> {
    return this.request<User>(`/users/${id}`, {
      method: 'PUT',
      body: JSON.stringify(userData),
    });
  }

  async deleteUser(id: string): Promise<ApiResponse<void>> {
    return this.request<void>(`/users/${id}`, {
      method: 'DELETE',
    });
  }

  // Notification API methods
  async getNotifications(params?: {
    page?: number;
    limit?: number;
    unreadOnly?: boolean;
    type?: string;
    category?: string;
  }): Promise<
    ApiResponse<{
      notifications: Notification[];
      unreadCount: number;
      pagination: PaginationInfo;
    }>
  > {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.unreadOnly)
      queryParams.append('unreadOnly', params.unreadOnly.toString());
    if (params?.type) queryParams.append('type', params.type);
    if (params?.category) queryParams.append('category', params.category);

    const queryString = queryParams.toString();
    const url = `/notifications${queryString ? `?${queryString}` : ''}`;

    return this.request(url);
  }

  async getUnreadCount(): Promise<ApiResponse<{ unreadCount: number }>> {
    return this.request('/notifications/unread-count');
  }

  async markNotificationAsRead(id: string): Promise<ApiResponse<Notification>> {
    return this.request(`/notifications/${id}/read`, {
      method: 'PUT',
    });
  }

  async markAllNotificationsAsRead(): Promise<
    ApiResponse<{ modifiedCount: number }>
  > {
    return this.request('/notifications/read-all', {
      method: 'PUT',
    });
  }

  async deleteNotification(id: string): Promise<ApiResponse<void>> {
    return this.request(`/notifications/${id}`, {
      method: 'DELETE',
    });
  }

  async getNotificationPreferences(): Promise<
    ApiResponse<NotificationPreferences>
  > {
    return this.request('/notifications/preferences');
  }

  async updateNotificationPreferences(
    preferences: Partial<NotificationPreferences>,
  ): Promise<ApiResponse<NotificationPreferences>> {
    return this.request('/notifications/preferences', {
      method: 'PUT',
      body: JSON.stringify({ preferences }),
    });
  }
}

// Create and export API client instance
export const apiClient = new ApiClient(API_BASE_URL);

// Export individual API functions for convenience
export const authApi = {
  login: (email: string, password: string) => apiClient.login(email, password),
  register: (userData: Parameters<typeof apiClient.register>[0]) =>
    apiClient.register(userData),
  logout: () => apiClient.logout(),
  getCurrentUser: () => apiClient.getCurrentUser(),
};

export const policyApi = {
  getPolicies: (params?: Parameters<typeof apiClient.getPolicies>[0]) =>
    apiClient.getPolicies(params),
  getPolicyById: (id: string) => apiClient.getPolicyById(id),
  requestPolicyInitiation: (
    policyData: Parameters<typeof apiClient.requestPolicyInitiation>[0],
  ) => apiClient.requestPolicyInitiation(policyData),
  updatePolicy: (
    id: string,
    policyData: Parameters<typeof apiClient.updatePolicy>[1],
  ) => apiClient.updatePolicy(id, policyData),
  deletePolicy: (id: string) => apiClient.deletePolicy(id),
  updatePolicyStatus: (id: string, status: string, comments?: string) =>
    apiClient.updatePolicyStatus(id, status, comments),
  approvePolicy: (id: string, comments?: string) =>
    apiClient.approvePolicy(id, comments),
  rejectPolicy: (id: string, comments?: string) =>
    apiClient.rejectPolicy(id, comments),
  governanceReview: (
    id: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ) => apiClient.governanceReview(id, decision, comments),
  grcReview: (
    id: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ) => apiClient.grcReview(id, decision, comments),
  submitPolicyForReview: (id: string, comments?: string) =>
    apiClient.submitPolicyForReview(id, comments),
  getPolicyStats: () => apiClient.getPolicyStats(),
  uploadAttachment: (id: string, file: File) =>
    apiClient.uploadPolicyAttachment(id, file),
};

export const userApi = {
  getUsers: () => apiClient.getUsers(),
  getUserById: (id: string) => apiClient.getUserById(id),
  createUser: (userData: {
    name: string;
    email: string;
    role: string;
    department: string;
    password: string;
  }) => apiClient.createUser(userData),
  updateUser: (
    id: string,
    userData: {
      name: string;
      email: string;
      role: string;
      department: string;
      password?: string;
    },
  ) => apiClient.updateUser(id, userData),
  deleteUser: (id: string) => apiClient.deleteUser(id),
};

// Notification types
export interface Notification {
  _id: string;
  title: string;
  message: string;
  type: 'policy_workflow' | 'user_management' | 'system' | 'reminder';
  category:
    | 'approval_required'
    | 'status_change'
    | 'assignment'
    | 'due_date'
    | 'system_update';
  isRead: boolean;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: string;
  data?: {
    policyId?: string;
    policyName?: string;
    actionRequired?: boolean;
    dueDate?: string;
    actionUrl?: string;
  };
  sender?: {
    name: string;
    email: string;
  };
  timeAgo?: string;
}

export interface NotificationPreferences {
  email: boolean;
  inApp: boolean;
  push: boolean;
  types: {
    policyWorkflow: boolean;
    assignments: boolean;
    reminders: boolean;
    systemUpdates: boolean;
  };
  frequency: 'immediate' | 'hourly' | 'daily';
  quietHours: {
    enabled: boolean;
    start: string;
    end: string;
  };
}

// Notification API
export const notificationApi = {
  getNotifications: (params?: {
    page?: number;
    limit?: number;
    unreadOnly?: boolean;
    type?: string;
    category?: string;
  }) => apiClient.getNotifications(params),
  getUnreadCount: () => apiClient.getUnreadCount(),
  markAsRead: (id: string) => apiClient.markNotificationAsRead(id),
  markAllAsRead: () => apiClient.markAllNotificationsAsRead(),
  deleteNotification: (id: string) => apiClient.deleteNotification(id),
  getPreferences: () => apiClient.getNotificationPreferences(),
  updatePreferences: (preferences: Partial<NotificationPreferences>) =>
    apiClient.updateNotificationPreferences(preferences),
};
