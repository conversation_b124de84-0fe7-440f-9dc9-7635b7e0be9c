{"name": "grc-web-app-backend", "version": "1.0.0", "description": "Backend API for GRC Web Application", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "seed": "node src/scripts/seedData.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["grc", "governance", "risk", "compliance", "policy", "management"], "author": "GRC Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "fs-extra": "^11.3.0", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "uuid": "^9.0.1"}, "devDependencies": {"eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.1", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}