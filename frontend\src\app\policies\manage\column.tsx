'use client';

import * as React from 'react';
import { createColumnHelper } from '@tanstack/react-table';
import { Checkbox } from '@/components/ui/checkbox';
import { ArrowUpDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { StatusBadge } from '@/components/StatusBadge';

export type Programs = {
  id: number;
  policyId: string;
  documentCode: string;
  name: string;
  documentName: string;
  documentType: string;
  version: string;
  versionNumber: number;
  policyType: string;
  categories: string;
  department: string;
  subDepartment: string;
  policyOwner: string;
  priorityScore: number;
  status: string;
  detailedStatus: string;
  classification: string;
  startDate: string;
  dueDate: string;
  completionDate: string;
  approvalDate: string;
  authorizedApprover: string;
  lastReviewDate: string;
  nextReviewDate: string;
  initiatedBy: string;
  reviewedBy: string;
  endorsedBy: string;
};

const columnHelper = createColumnHelper<Programs>();

export const columns = [
  columnHelper.display({
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        className="border-customBorder data-[state=checked]:bg-customGreen"
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        className="border-customBorder data-[state=checked]:bg-customGreen"
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  }),
  // 1. Policy ID - Unique identifier
  columnHelper.accessor('policyId', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Policy ID
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      return (
        <div className="flex items-center space-x-2 text-sm font-medium text-customBlueSecondary">
          <span>{row.getValue('policyId')}</span>
        </div>
      );
    },
    enableHiding: false,
  }),
  // 2. Policy Name - Main identifier
  columnHelper.accessor('name', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Policy Name
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div
        className="max-w-[200px] truncate text-sm font-medium text-customBlueSecondary"
        title={row.getValue('name')}
      >
        {row.getValue('name')}
      </div>
    ),
    enableHiding: false,
  }),

  // 3. Document Type - Critical for categorization
  columnHelper.accessor('documentType', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">Type</div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-sm font-medium text-customBlueSecondary">
        {row.getValue('documentType') || 'Policy'}
      </div>
    ),
    enableHiding: false,
  }),
  // 4. Department - Ownership and responsibility
  columnHelper.accessor('department', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Department
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-sm font-medium text-customBlueSecondary">
        {row.getValue('department')}
      </div>
    ),
    enableHiding: false,
  }),

  // 5. Policy Owner - Key stakeholder
  columnHelper.accessor('policyOwner', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">Owner</div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div
        className="max-w-[120px] truncate text-sm font-medium text-customBlueSecondary"
        title={row.getValue('policyOwner')}
      >
        {row.getValue('policyOwner')}
      </div>
    ),
    enableHiding: false,
  }),
  // 6. Priority Score - Business importance
  columnHelper.accessor('priorityScore', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Priority
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const score = row.getValue('priorityScore') as number;
      const getScoreColor = (score: number) => {
        if (score >= 9) return 'text-red-600 bg-red-50';
        if (score >= 7) return 'text-orange-600 bg-orange-50';
        if (score >= 4) return 'text-yellow-600 bg-yellow-50';
        return 'text-green-600 bg-green-50';
      };
      return (
        <div
          className={`rounded-full px-2 py-1 text-xs font-medium ${getScoreColor(score || 5)}`}
        >
          {score || 5}/10
        </div>
      );
    },
    enableHiding: false,
  }),

  // 7. Status - Current lifecycle stage
  columnHelper.accessor('status', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">Status</div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const status = row.getValue('status') as string;
      return <StatusBadge status={status} />;
    },
    enableHiding: false,
  }),

  // 8. Classification - Security level
  columnHelper.accessor('classification', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Classification
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const classification = row.getValue('classification') as string;
      const getClassificationColor = (classification: string) => {
        switch (classification) {
          case 'Top Secret':
            return 'text-red-700 bg-red-100';
          case 'Restricted':
            return 'text-red-600 bg-red-50';
          case 'Confidential':
            return 'text-orange-600 bg-orange-50';
          case 'Internal':
            return 'text-blue-600 bg-blue-50';
          case 'Public':
            return 'text-green-600 bg-green-50';
          default:
            return 'text-gray-600 bg-gray-50';
        }
      };
      return (
        <div
          className={`rounded-full px-2 py-1 text-xs font-medium ${getClassificationColor(classification || 'Internal')}`}
        >
          {classification || 'Internal'}
        </div>
      );
    },
    enableHiding: false,
  }),

  // 9. Due Date - Critical for tracking
  columnHelper.accessor('dueDate', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Due Date
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => {
      const dueDate = row.getValue('dueDate') as string;
      const isOverdue = dueDate && new Date(dueDate) < new Date();
      return (
        <div
          className={`text-sm font-medium ${isOverdue ? 'text-red-600' : 'text-customBlueSecondary'}`}
        >
          {dueDate ? new Date(dueDate).toLocaleDateString() : 'N/A'}
        </div>
      );
    },
    enableHiding: false,
  }),
  // 10. Version - Document version tracking
  columnHelper.accessor('version', {
    header: ({ column }) => (
      <div className="flex items-center justify-between">
        <div className="text-sm font-bold text-customBlueSecondary">
          Version
        </div>
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          <ArrowUpDown className="h-4 w-4 text-customBlueSecondary" />
        </Button>
      </div>
    ),
    cell: ({ row }) => (
      <div className="text-sm font-medium text-customBlueSecondary">
        {row.getValue('version') || '1.0'}
      </div>
    ),
    enableHiding: false,
  }),
];
