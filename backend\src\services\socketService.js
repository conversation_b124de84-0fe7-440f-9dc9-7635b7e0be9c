const { Server } = require('socket.io');
const jwt = require('jsonwebtoken');
const User = require('../models/User');

class SocketService {
  constructor() {
    this.io = null;
    this.connectedUsers = new Map(); // userId -> socketId mapping
  }

  // Initialize Socket.IO server
  initialize(server) {
    this.io = new Server(server, {
      cors: {
        origin: "*",
        methods: ["GET", "POST"],
        credentials: false,
      },
      transports: ['websocket', 'polling'],
    });

    this.setupMiddleware();
    this.setupEventHandlers();

    console.log('Socket.IO server initialized');
    return this.io;
  }

  // Setup authentication middleware
  setupMiddleware() {
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.split(' ')[1];
        
        if (!token) {
          return next(new Error('Authentication token required'));
        }

        // Verify JWT token
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        
        // Get user from database
        const user = await User.findById(decoded.id).select('-password');
        
        if (!user || !user.isActive) {
          return next(new Error('Invalid or inactive user'));
        }

        // Attach user to socket
        socket.userId = user._id.toString();
        socket.user = user;
        
        next();
      } catch (error) {
        console.error('Socket authentication error:', error.message);
        next(new Error('Authentication failed'));
      }
    });
  }

  // Setup event handlers
  setupEventHandlers() {
    this.io.on('connection', (socket) => {
      console.log(`User ${socket.user.name} connected (${socket.userId})`);
      
      // Join user-specific room
      socket.join(`user_${socket.userId}`);
      
      // Join role-specific room
      socket.join(`role_${socket.user.role}`);
      
      // Join department-specific room
      if (socket.user.department) {
        socket.join(`department_${socket.user.department}`);
      }

      // Store connection
      this.connectedUsers.set(socket.userId, socket.id);

      // Handle notification events
      this.setupNotificationHandlers(socket);

      // Handle disconnection
      socket.on('disconnect', () => {
        console.log(`User ${socket.user.name} disconnected (${socket.userId})`);
        this.connectedUsers.delete(socket.userId);
      });

      // Send connection confirmation
      socket.emit('connected', {
        message: 'Connected to notification service',
        userId: socket.userId,
        timestamp: new Date().toISOString(),
      });
    });
  }

  // Setup notification-specific event handlers
  setupNotificationHandlers(socket) {
    // Mark notification as read
    socket.on('mark_notification_read', async (data) => {
      try {
        const { notificationId } = data;
        const Notification = require('../models/Notification');
        
        await Notification.findOneAndUpdate(
          { _id: notificationId, 'recipient.id': socket.userId },
          { isRead: true, readAt: new Date() }
        );

        socket.emit('notification_marked_read', { notificationId });
      } catch (error) {
        console.error('Error marking notification as read:', error);
        socket.emit('error', { message: 'Failed to mark notification as read' });
      }
    });

    // Get unread count
    socket.on('get_unread_count', async () => {
      try {
        const Notification = require('../models/Notification');
        const unreadCount = await Notification.getUnreadCount(socket.userId);
        
        socket.emit('unread_count_update', { unreadCount });
      } catch (error) {
        console.error('Error getting unread count:', error);
        socket.emit('error', { message: 'Failed to get unread count' });
      }
    });

    // Join policy-specific room for policy updates
    socket.on('join_policy_room', (data) => {
      const { policyId } = data;
      if (policyId) {
        socket.join(`policy_${policyId}`);
        socket.emit('joined_policy_room', { policyId });
      }
    });

    // Leave policy-specific room
    socket.on('leave_policy_room', (data) => {
      const { policyId } = data;
      if (policyId) {
        socket.leave(`policy_${policyId}`);
        socket.emit('left_policy_room', { policyId });
      }
    });
  }

  // Send notification to specific user
  sendToUser(userId, event, data) {
    if (this.io) {
      this.io.to(`user_${userId}`).emit(event, data);
    }
  }

  // Send notification to users with specific role
  sendToRole(role, event, data) {
    if (this.io) {
      this.io.to(`role_${role}`).emit(event, data);
    }
  }

  // Send notification to users in specific department
  sendToDepartment(department, event, data) {
    if (this.io) {
      this.io.to(`department_${department}`).emit(event, data);
    }
  }

  // Send notification to users following a specific policy
  sendToPolicyFollowers(policyId, event, data) {
    if (this.io) {
      this.io.to(`policy_${policyId}`).emit(event, data);
    }
  }

  // Broadcast to all connected users
  broadcast(event, data) {
    if (this.io) {
      this.io.emit(event, data);
    }
  }

  // Get connected users count
  getConnectedUsersCount() {
    return this.connectedUsers.size;
  }

  // Check if user is connected
  isUserConnected(userId) {
    return this.connectedUsers.has(userId);
  }

  // Get Socket.IO instance
  getIO() {
    return this.io;
  }

  // Send real-time notification
  sendNotification(notification) {
    if (!this.io) return;

    const recipientId = notification.recipient.id.toString();
    
    // Send to specific user
    this.sendToUser(recipientId, 'new_notification', notification);

    // Send unread count update
    this.updateUnreadCount(recipientId);

    // If it's a policy-related notification, send to policy followers
    if (notification.data?.policyId) {
      this.sendToPolicyFollowers(notification.data.policyId, 'policy_notification', {
        policyId: notification.data.policyId,
        notification,
      });
    }
  }

  // Update unread count for user
  async updateUnreadCount(userId) {
    try {
      const Notification = require('../models/Notification');
      const unreadCount = await Notification.getUnreadCount(userId);
      
      this.sendToUser(userId, 'unread_count_update', { unreadCount });
    } catch (error) {
      console.error('Error updating unread count:', error);
    }
  }

  // Send system announcement
  sendSystemAnnouncement(message, priority = 'medium') {
    this.broadcast('system_announcement', {
      message,
      priority,
      timestamp: new Date().toISOString(),
    });
  }

  // Send maintenance notification
  sendMaintenanceNotification(message, scheduledTime) {
    this.broadcast('maintenance_notification', {
      message,
      scheduledTime,
      timestamp: new Date().toISOString(),
    });
  }
}

// Create singleton instance
const socketService = new SocketService();

module.exports = socketService;
