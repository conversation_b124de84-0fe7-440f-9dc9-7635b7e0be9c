const express = require('express');
const router = express.Router();
const {
  getAllPolicies,
  getPolicyById,
  requestPolicyInitiation,
  updatePolicy,
  deletePolicy,
  updatePolicyStatus,
  getPolicyStats,
  approvePolicy,
  rejectPolicy,
  getPolicyHistory,
  governanceReview,
  grcReview,
  submitPolicyForReview,
  uploadPolicyAttachment,
} = require('../controllers/policyController');

const { protect, authorize, checkPermission, checkOwnership } = require('../middleware/auth');
const { validatePolicyRequest, validatePolicyUpdate } = require('../middleware/validation');

// Public routes (with optional auth)
router.get('/stats', getPolicyStats);

// Protected routes - require authentication
router.use(protect);

// GET /api/policies - Get all policies with filtering and pagination
router.get('/', getAllPolicies);

// GET /api/policies/:id - Get specific policy by ID
router.get('/:id', getPolicyById);

// POST /api/policies/request - Request policy initiation (Creator, Admin, Super Admin only)
router.post('/request', authorize('Creator', 'Admin', 'Super Admin'), validatePolicyRequest, requestPolicyInitiation);

// PUT /api/policies/:id - Update policy (only owner or admin)
router.put('/:id', validatePolicyUpdate, updatePolicy);

// DELETE /api/policies/:id - Delete policy (only owner or admin)
router.delete('/:id', deletePolicy);

// PUT /api/policies/:id/status - Update policy status
router.put('/:id/status', updatePolicyStatus);

// POST /api/policies/:id/approve - Approve policy
router.post('/:id/approve', checkPermission('approve_policy'), approvePolicy);

// POST /api/policies/:id/reject - Reject policy
router.post('/:id/reject', checkPermission('approve_policy'), rejectPolicy);

// GET /api/policies/:id/history - Get policy history/audit trail
router.get('/:id/history', getPolicyHistory);

// POST /api/policies/:id/governance-review - Governance review (Approver from Governance dept, Admin, Super Admin)
router.post('/:id/governance-review', authorize('Approver', 'Admin', 'Super Admin'), governanceReview);

// POST /api/policies/:id/grc-review - GRC review (Reviewer from GRC dept, Admin, Super Admin)
router.post('/:id/grc-review', authorize('Reviewer', 'Admin', 'Super Admin'), grcReview);

// POST /api/policies/:id/submit-for-review - Submit policy for review (Draft → Under Review)
router.post('/:id/submit-for-review', submitPolicyForReview);

// Note: File upload routes moved to /api/uploads/policies/:id/attachments

module.exports = router;
