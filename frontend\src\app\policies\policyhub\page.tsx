'use client';

import { useState, useEffect } from 'react';
import { DataTable } from '@/app/policies/policyhub/components/datatable';
import { columns } from '@/app/policies/policyhub/components/column';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ErrorDisplay } from '@/components/ui/error-display';
import { LoadingDisplay } from '@/components/ui/loading-display';
import { PolicyDetailsModal } from '@/components/PolicyDetailsModal';
import { useRouter } from 'next/navigation';
import { usePolicies } from '@/hooks/usePolicies';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { Policy } from '@/lib/api';

const Policyhub = () => {
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [selectedPolicy, setSelectedPolicy] = useState<Policy | null>(null);

  const { toast } = useToast();
  const { user, isAuthenticated, loading: authLoading } = useAuth();
  const router = useRouter();

  // Fetch only published policies
  const {
    policies,
    metadata,
    loading: policiesLoading,
    error: policiesError,
    refetch: refetchPolicies,
    updatePolicyStatus,
    governanceReview,
    grcReview,
    submitPolicyForReview,
    approvePolicy,
    rejectPolicy,
  } = usePolicies({
    status: 'Published', // Filter for published policies only
    autoFetch: false,
  });

  // Trigger policies fetch when authentication is ready
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      refetchPolicies();
    }
  }, [isAuthenticated, authLoading, refetchPolicies]);

  // Handle policy row click to open details modal
  const handlePolicyClick = (policy: Policy) => {
    setSelectedPolicy(policy);
    setIsDetailsModalOpen(true);
  };

  // Handle closing details modal
  const handleCloseDetailsModal = () => {
    setIsDetailsModalOpen(false);
    setSelectedPolicy(null);
  };

  // Handle status update from details modal
  const handleStatusUpdate = async (
    policyId: string,
    newStatus: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await updatePolicyStatus(policyId, newStatus, comments);
    // Refresh data after status update
    refetchPolicies();
    return result !== null;
  };

  // Handle governance review from details modal
  const handleGovernanceReview = async (
    policyId: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ): Promise<boolean> => {
    const result = await governanceReview(policyId, decision, comments);
    // Refresh data after governance review
    refetchPolicies();
    return result;
  };

  // Handle GRC review from details modal
  const handleGrcReview = async (
    policyId: string,
    decision: 'Approved' | 'Rejected',
    comments?: string,
  ): Promise<boolean> => {
    const result = await grcReview(policyId, decision, comments);
    // Refresh data after GRC review
    refetchPolicies();
    return result;
  };

  // Handle submit for review from details modal
  const handleSubmitForReview = async (
    policyId: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await submitPolicyForReview(policyId, comments);
    // Refresh data after submission
    refetchPolicies();
    return result;
  };

  // Handle approve policy from details modal
  const handleApprovePolicy = async (
    policyId: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await approvePolicy(policyId, comments);
    // Refresh data after approval
    refetchPolicies();
    return result;
  };

  // Handle reject policy from details modal
  const handleRejectPolicy = async (
    policyId: string,
    comments?: string,
  ): Promise<boolean> => {
    const result = await rejectPolicy(policyId, comments);
    // Refresh data after rejection
    refetchPolicies();
    return result;
  };

  // Transform API data to match table structure
  const transformedPolicies = policies.map((policy, index) => ({
    id: index,
    policyId: policy.policyId,
    documentCode: policy.documentCode || '',
    name: policy.name,
    documentName: policy.documentName || policy.name,
    documentType: policy.documentType || 'Policy',
    version: policy.version || '1.0',
    versionNumber: policy.versionNumber || 1.0,
    policyType: policy.policyType || 'Corporate',
    categories: policy.categories.join(', '),
    department: policy.department,
    subDepartment: policy.subDepartment || '',
    policyOwner: policy.policyOwner.name,
    priorityScore: policy.priorityScore || 5,
    status: policy.status,
    detailedStatus: policy.detailedStatus || '',
    classification: policy.classification || 'Internal',
    startDate: policy.startDate || '',
    dueDate: policy.dueDate || '',
    completionDate: policy.completionDate || '',
    approvalDate: policy.approvalDate || '',
    authorizedApprover: policy.authorizedApprover?.name || '',
    lastReviewDate: policy.lastReviewDate || '',
    nextReviewDate: policy.nextReviewDate || '',
    initiatedBy: policy.initiatedBy?.name || '',
    reviewedBy: policy.reviewedBy?.map((r) => r.name).join(', ') || '',
    endorsedBy: policy.endorsedBy?.map((e) => e.name).join(', ') || '',
    publishedDate: policy.publishedDate || '',
    effectiveDate: policy.effectiveDate || '',
  }));

  // Calculate category counts from published policies
  const categoryCounts = policies.reduce(
    (acc, policy) => {
      policy.categories.forEach((category) => {
        acc[category] = (acc[category] || 0) + 1;
      });
      return acc;
    },
    {} as Record<string, number>,
  );

  // Create category cards based on actual published policies data
  const cardData = [
    {
      title: 'All Published',
      number: policies.length.toString(),
      borderColor: 'border-l-[#1EE0AC]',
      numberColor: 'text-[#1EE0AC]',
      description: 'Total published policies',
    },
    {
      title: 'Corporate',
      number: (categoryCounts['Corporate'] || 0).toString(),
      borderColor: 'border-l-[#E85347]',
      numberColor: 'text-[#E85347]',
      description: 'Corporate policies',
    },
    {
      title: 'Compliance',
      number: (categoryCounts['Compliance'] || 0).toString(),
      borderColor: 'border-l-[#F4BD0E]',
      numberColor: 'text-[#F4BD0E]',
      description: 'Compliance policies',
    },
    {
      title: 'Organizational',
      number: (categoryCounts['Organizational'] || 0).toString(),
      borderColor: 'border-l-[#8B5CF6]',
      numberColor: 'text-[#8B5CF6]',
      description: 'Organizational policies',
    },
  ];

  // Show loading while checking authentication
  if (authLoading) {
    return (
      <LoadingDisplay
        message="Checking authentication..."
        variant="default"
        size="md"
      />
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    router.push('/login');
    return (
      <LoadingDisplay
        message="Redirecting to login..."
        variant="default"
        size="md"
      />
    );
  }

  // Show loading while fetching policies data
  if (policiesLoading) {
    return (
      <LoadingDisplay
        message="Loading published policies..."
        variant="default"
        size="md"
      />
    );
  }

  if (policiesError) {
    return (
      <div className="bg-gray-100 py-2">
        <ErrorDisplay
          variant="minimal"
          title="Unable to Load Policies"
          message="We couldn't load the published policies. Please try again."
          error={policiesError || undefined}
          onRetry={() => refetchPolicies()}
          showTechnicalDetails={true}
        />
      </div>
    );
  }

  return (
    <>
      <div className="bg-gray-100 py-2 font-roboto">
        <div className="mb-4 flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-customBlue">Policy Hub</h1>
            <p className="mt-1 text-sm text-gray-600">
              Browse and access all published policies
            </p>
          </div>
        </div>

        {/* Summary Card */}
        <div className="mb-6">
          <Card className="border-l-4 border-l-customBlue bg-gradient-to-r from-green-50 to-blue-50">
            <CardHeader>
              <CardTitle className="flex items-center justify-between text-lg font-bold text-customBlue">
                <div className="flex items-center space-x-3">
                  <span>Published Policy Hub</span>
                </div>
                <div className="text-3xl font-bold text-customBlue">
                  {policiesLoading ? '...' : policies.length}
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                Access all published and active policies. These policies are
                officially approved and in effect.
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Category Cards */}
        <div className="mb-6">
          <h2 className="mb-3 text-lg font-semibold text-gray-700">
            Policy Categories
          </h2>
          <div className="flex w-full items-center justify-between space-x-2">
            {cardData.map((card, index) => (
              <Card
                key={index}
                className={`flex-1 ${card.borderColor} cursor-pointer border-l-4 transition-shadow hover:shadow-md`}
              >
                <CardHeader className="py-4">
                  <CardTitle className="flex items-center justify-between text-base font-bold text-customGray">
                    {card.title}{' '}
                    <span className="flex h-4 w-4 items-center justify-center rounded-full bg-gray-400 text-[9px] font-bold text-white">
                      ?
                    </span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="flex flex-col space-y-2 pb-4">
                  <p
                    className={`text-3xl font-bold ${card.numberColor} ml-auto`}
                  >
                    {policiesLoading ? '...' : card.number}
                  </p>
                  <p className="text-xs text-gray-500">{card.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        <div className="mt-4">
          <DataTable
            columns={columns}
            data={transformedPolicies}
            loading={policiesLoading}
            onRowClick={(row) => {
              // Find the original policy from the transformed data
              const originalPolicy = policies.find(
                (p) => p._id === row.policyId || p.policyId === row.policyId,
              );
              if (originalPolicy) {
                handlePolicyClick(originalPolicy);
              }
            }}
          />
        </div>
      </div>

      <PolicyDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={handleCloseDetailsModal}
        policy={selectedPolicy}
        onStatusUpdate={handleStatusUpdate}
        onGovernanceReview={handleGovernanceReview}
        onGrcReview={handleGrcReview}
        onSubmitForReview={handleSubmitForReview}
        onApprovePolicy={handleApprovePolicy}
        onRejectPolicy={handleRejectPolicy}
        onPolicyUpdate={refetchPolicies}
        userRole={user?.role || 'Viewer'}
        loading={false}
      />
    </>
  );
};

export default Policyhub;
