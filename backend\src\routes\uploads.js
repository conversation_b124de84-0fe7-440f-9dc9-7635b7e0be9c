const express = require('express');
const router = express.Router();
const { uploadPolicyAttachment } = require('../controllers/policyController');
const { uploadSingle } = require('../middleware/upload');
const { protect } = require('../middleware/auth');

// POST /api/uploads/policies/:id/attachments - Upload policy attachments
router.post('/policies/:id/attachments', protect, uploadSingle, uploadPolicyAttachment);

module.exports = router;
