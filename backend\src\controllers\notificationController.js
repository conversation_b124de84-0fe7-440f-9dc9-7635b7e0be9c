const Notification = require('../models/Notification');
const User = require('../models/User');

// @desc    Get user notifications
// @route   GET /api/notifications
// @access  Private
const getNotifications = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 20,
      unreadOnly = false,
      type,
      category,
    } = req.query;

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      unreadOnly: unreadOnly === 'true',
      type,
      category,
    };

    const notifications = await Notification.findUserNotifications(req.user._id, options);
    const unreadCount = await Notification.getUnreadCount(req.user._id);
    const total = await Notification.countDocuments({
      'recipient.id': req.user._id,
      isArchived: { $ne: true },
      ...(options.unreadOnly && { isRead: false }),
      ...(options.type && { type: options.type }),
      ...(options.category && { category: options.category }),
    });

    res.status(200).json({
      success: true,
      data: {
        notifications,
        unreadCount,
        pagination: {
          currentPage: options.page,
          totalPages: Math.ceil(total / options.limit),
          totalItems: total,
          itemsPerPage: options.limit,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get unread notification count
// @route   GET /api/notifications/unread-count
// @access  Private
const getUnreadCount = async (req, res, next) => {
  try {
    const unreadCount = await Notification.getUnreadCount(req.user._id);

    res.status(200).json({
      success: true,
      data: { unreadCount },
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Mark notification as read
// @route   PUT /api/notifications/:id/read
// @access  Private
const markAsRead = async (req, res, next) => {
  try {
    const notification = await Notification.findOne({
      _id: req.params.id,
      'recipient.id': req.user._id,
    });

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: 'Notification not found',
      });
    }

    notification.isRead = true;
    notification.readAt = new Date();
    await notification.save();

    res.status(200).json({
      success: true,
      data: notification,
      message: 'Notification marked as read',
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Mark all notifications as read
// @route   PUT /api/notifications/read-all
// @access  Private
const markAllAsRead = async (req, res, next) => {
  try {
    const result = await Notification.markAsRead(req.user._id);

    res.status(200).json({
      success: true,
      data: { modifiedCount: result.modifiedCount },
      message: 'All notifications marked as read',
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Delete notification
// @route   DELETE /api/notifications/:id
// @access  Private
const deleteNotification = async (req, res, next) => {
  try {
    const notification = await Notification.findOneAndDelete({
      _id: req.params.id,
      'recipient.id': req.user._id,
    });

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: 'Notification not found',
      });
    }

    res.status(200).json({
      success: true,
      message: 'Notification deleted successfully',
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Archive notification
// @route   PUT /api/notifications/:id/archive
// @access  Private
const archiveNotification = async (req, res, next) => {
  try {
    const notification = await Notification.findOne({
      _id: req.params.id,
      'recipient.id': req.user._id,
    });

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: 'Notification not found',
      });
    }

    notification.isArchived = true;
    notification.archivedAt = new Date();
    await notification.save();

    res.status(200).json({
      success: true,
      data: notification,
      message: 'Notification archived successfully',
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Get notification preferences
// @route   GET /api/notifications/preferences
// @access  Private
const getPreferences = async (req, res, next) => {
  try {
    const user = await User.findById(req.user._id).select('preferences.notifications');

    res.status(200).json({
      success: true,
      data: user.preferences?.notifications || {
        email: true,
        inApp: true,
        push: false,
        types: {
          policyWorkflow: true,
          assignments: true,
          reminders: true,
          systemUpdates: true,
        },
        frequency: 'immediate',
        quietHours: {
          enabled: false,
          start: '22:00',
          end: '08:00',
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Update notification preferences
// @route   PUT /api/notifications/preferences
// @access  Private
const updatePreferences = async (req, res, next) => {
  try {
    const { preferences } = req.body;

    const user = await User.findById(req.user._id);
    
    if (!user.preferences) {
      user.preferences = {};
    }
    
    user.preferences.notifications = {
      ...user.preferences.notifications,
      ...preferences,
    };

    await user.save();

    res.status(200).json({
      success: true,
      data: user.preferences.notifications,
      message: 'Notification preferences updated successfully',
    });
  } catch (error) {
    next(error);
  }
};

// @desc    Create notification (Admin only)
// @route   POST /api/notifications
// @access  Private (Admin)
const createNotification = async (req, res, next) => {
  try {
    const {
      recipientId,
      type,
      category,
      title,
      message,
      priority = 'medium',
      data = {},
      expiresAt,
    } = req.body;

    // Get recipient user
    const recipient = await User.findById(recipientId);
    if (!recipient) {
      return res.status(404).json({
        success: false,
        message: 'Recipient user not found',
      });
    }

    const notificationData = {
      recipient: {
        id: recipient._id,
        email: recipient.email,
        role: recipient.role,
      },
      sender: {
        id: req.user._id,
        name: req.user.name,
        email: req.user.email,
      },
      type,
      category,
      title,
      message,
      priority,
      data,
      expiresAt,
    };

    const notification = await Notification.createNotification(notificationData);

    // Emit real-time notification if WebSocket is available
    if (req.io) {
      req.io.to(`user_${recipientId}`).emit('new_notification', notification);
    }

    res.status(201).json({
      success: true,
      data: notification,
      message: 'Notification created successfully',
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getNotifications,
  getUnreadCount,
  markAsRead,
  markAllAsRead,
  deleteNotification,
  archiveNotification,
  getPreferences,
  updatePreferences,
  createNotification,
};
