const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const User = require('../models/User');
const Policy = require('../models/Policy');

const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/grc-web-app');
    console.log('📦 MongoDB Connected for seeding');
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    process.exit(1);
  }
};

const seedUsers = async () => {
  try {
    // Clear existing users
    await User.deleteMany({});
    console.log('🗑️  Cleared existing users');

    // Create users with correct role-department combinations for workflow
    const users = [
      {
        name: 'Super Admin User',
        email: '<EMAIL>',
        password: 'password123',
        role: 'Super Admin',
        department: 'IT',
        permissions: [
          'create_policy', 'edit_policy', 'delete_policy', 'approve_policy',
          'view_all_policies', 'manage_users', 'view_reports', 'export_data'
        ],
      },
      {
        name: 'Admin User',
        email: '<EMAIL>',
        password: 'password123',
        role: 'Admin',
        department: 'IT',
        permissions: [
          'create_policy', 'edit_policy', 'delete_policy', 'approve_policy',
          'view_all_policies', 'manage_users', 'view_reports'
        ],
      },
      // Workflow-specific users
      {
        name: 'Governance Reviewer',
        email: '<EMAIL>',
        password: 'password123',
        role: 'Reviewer',
        department: 'Governance',
        permissions: ['view_all_policies', 'view_reports'],
      },
      {
        name: 'GRC Reviewer',
        email: '<EMAIL>',
        password: 'password123',
        role: 'Reviewer',
        department: 'GRC',
        permissions: ['view_all_policies', 'view_reports'],
      },
      {
        name: 'Policy Creator',
        email: '<EMAIL>*',
        password: 'password123',
        role: 'Creator',
        department: 'HR',
        permissions: ['create_policy', 'edit_policy', 'view_reports'],
      },
      {
        name: 'CEO Approver',
        email: '<EMAIL>',
        password: 'password123',
        role: 'Approver',
        department: 'CEO Office',
        permissions: ['approve_policy', 'view_all_policies', 'view_reports'],
      },
      {
        name: 'Policy Publisher',
        email: '<EMAIL>',
        password: 'password123',
        role: 'Publisher',
        department: 'Governance',
        permissions: ['view_all_policies', 'view_reports'],
      },
      {
        name: 'General Viewer',
        email: '<EMAIL>',
        password: 'password123',
        role: 'Viewer',
        department: 'Finance',
        permissions: ['view_reports'],
      },
    ];

    // Create users one by one to trigger pre-save middleware
    const createdUsers = [];
    for (const userData of users) {
      const user = new User(userData);
      await user.save();
      createdUsers.push(user);
    }

    console.log(`👥 Created ${createdUsers.length} demo users`);
    return createdUsers;
  } catch (error) {
    console.error('❌ Error seeding users:', error);
    throw error;
  }
};

const seedPolicies = async (users) => {
  try {
    // Clear existing policies
    await Policy.deleteMany({});
    console.log('🗑️  Cleared existing policies');

    // Find users for policy ownership
    const creatorUser = users.find(u => u.email === '<EMAIL>');
    const viewerUser = users.find(u => u.email === '<EMAIL>');
    const adminUser = users.find(u => u.email === '<EMAIL>');

    // Create demo policies with all required fields
    const policies = [
      {
        policyId: 'HR_ORG_001',
        name: 'Information Security Policy',
        description: 'Comprehensive policy for information security guidelines and procedures',
        version: '1.0',
        versionNumber: 1.0,
        categories: ['Organizational'],
        department: 'HR',
        policyCategory: 'Corporate Policies',
        approvalAuthority: 'Board Committee',
        policyOwner: {
          id: creatorUser._id,
          name: creatorUser.name,
          email: creatorUser.email,
        },
        status: 'Approved',
        priorityScore: 8,
        classification: 'Internal',
        metadata: {
          priority: 'High',
          tags: ['security', 'information', 'guidelines'],
        },
      },
      {
        policyId: 'HR_COM_002',
        name: 'Employee Flexibility Policy',
        description: 'Policy outlining flexible work arrangements and remote work guidelines',
        version: '1.2',
        versionNumber: 1.2,
        categories: ['HR'],
        department: 'HR',
        policyCategory: 'Operational Policies',
        approvalAuthority: 'CEO',
        policyOwner: {
          id: viewerUser._id,
          name: viewerUser.name,
          email: viewerUser.email,
        },
        status: 'Published',
        priorityScore: 6,
        classification: 'Internal',
        metadata: {
          priority: 'Medium',
          tags: ['flexibility', 'remote work', 'arrangements'],
        },
      },
      {
        policyId: 'IT_SEC_003',
        name: 'Data Protection and Privacy Policy',
        description: 'Policy for protecting sensitive data and ensuring privacy compliance',
        version: '0.1',
        versionNumber: 0.1,
        categories: ['Security'],
        department: 'IT',
        policyCategory: 'Corporate Policies',
        approvalAuthority: 'Board Committee',
        policyOwner: {
          id: creatorUser._id,
          name: creatorUser.name,
          email: creatorUser.email,
        },
        status: 'Request Initiated',
        priorityScore: 9,
        classification: 'Confidential',
        metadata: {
          priority: 'Critical',
          tags: ['data protection', 'privacy', 'compliance'],
        },
      },
      {
        policyId: 'HR_COM_004',
        name: 'Code of Conduct Policy',
        description: 'Organizational code of conduct and ethical guidelines',
        version: '2.0',
        versionNumber: 2.0,
        categories: ['Organizational'],
        department: 'HR',
        policyCategory: 'Corporate Policies',
        approvalAuthority: 'Board Committee',
        policyOwner: {
          id: viewerUser._id,
          name: viewerUser.name,
          email: viewerUser.email,
        },
        status: 'Under Review',
        priorityScore: 7,
        classification: 'Internal',
        metadata: {
          priority: 'High',
          tags: ['conduct', 'ethics', 'guidelines'],
        },
      },
      {
        policyId: 'IT_ORG_005',
        name: 'IT Asset Management Policy',
        description: 'Policy for managing IT assets, procurement, and disposal',
        version: '1.0',
        versionNumber: 1.0,
        categories: ['IT'],
        department: 'IT',
        policyCategory: 'Operational Policies',
        approvalAuthority: 'CEO',
        policyOwner: {
          id: creatorUser._id,
          name: creatorUser.name,
          email: creatorUser.email,
        },
        status: 'Draft',
        priorityScore: 5,
        classification: 'Internal',
        metadata: {
          priority: 'Medium',
          tags: ['assets', 'procurement', 'disposal'],
        },
      },
      {
        policyId: 'HR_COM_006',
        name: 'Workplace Safety Policy',
        description: 'Comprehensive workplace safety and health policy',
        version: '1.5',
        versionNumber: 1.5,
        categories: ['HR'],
        department: 'HR',
        policyCategory: 'Operational Policies',
        approvalAuthority: 'CEO',
        policyOwner: {
          id: adminUser._id,
          name: adminUser.name,
          email: adminUser.email,
        },
        status: 'Pending Approval',
        priorityScore: 8,
        classification: 'Internal',
        metadata: {
          priority: 'High',
          tags: ['safety', 'health', 'workplace'],
        },
      },
    ];

    const createdPolicies = await Policy.insertMany(policies);
    console.log(`📋 Created ${createdPolicies.length} demo policies`);
    return createdPolicies;
  } catch (error) {
    console.error('❌ Error seeding policies:', error);
    throw error;
  }
};

const seedDatabase = async () => {
  try {
    await connectDB();
    
    console.log('🌱 Starting database seeding...');
    
    const users = await seedUsers();
    const policies = await seedPolicies(users);
    
    console.log('✅ Database seeding completed successfully!');
    console.log(`📊 Summary: ${users.length} users, ${policies.length} policies`);
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Database seeding failed:', error);
    process.exit(1);
  }
};

// Run the seeding if this file is executed directly
if (require.main === module) {
  seedDatabase();
}

module.exports = { seedDatabase };
