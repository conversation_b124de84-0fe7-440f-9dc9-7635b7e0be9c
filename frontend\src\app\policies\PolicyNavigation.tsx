'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
// Utility function for conditional class names
const cn = (...classes: string[]) => {
  return classes.filter(Boolean).join(' ');
};

const PolicyNavigation = () => {
  const pathname = usePathname();

  const navItems = [
    { name: 'Dashboard', href: '/policies/dashboard' },
    { name: 'My Actions', href: '/policies/my-actions' },
    { name: 'Manage', href: '/policies/manage' },
    { name: 'Policy Hub', href: '/policies/policyhub' },
    { name: 'Repository', href: '/policies/repository' },
    { name: 'Reports', href: '/policies/reports_2' },
    { name: 'User Management', href: '/policies/user-management' },
  ];

  return (
    <div className="mb-6 border-b border-gray-200">
      <nav className="flex space-x-4 overflow-x-auto pb-2">
        {navItems.map((item) => (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              'whitespace-nowrap rounded-md px-3 py-2 text-sm font-medium transition-colors',
              pathname === item.href
                ? 'bg-customBlue text-white'
                : 'text-gray-600 hover:bg-gray-100',
            )}
          >
            {item.name}
          </Link>
        ))}
      </nav>
    </div>
  );
};

export default PolicyNavigation;
