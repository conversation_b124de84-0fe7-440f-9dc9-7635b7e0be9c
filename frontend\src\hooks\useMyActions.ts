import { useMemo } from 'react';
import { Policy } from '@/lib/api';
import { User } from '@/lib/api';

interface ActionCategories {
  urgent: Policy[];
  myDrafts: Policy[];
  awaitingMyReview: Policy[];
  awaitingMyApproval: Policy[];
  readyToPublish: Policy[];
  annualReview: Policy[];
}

interface UseMyActionsReturn {
  actionablePolicies: Policy[];
  actionCategories: ActionCategories;
  totalActionCount: number;
  hasUrgentActions: boolean;
}

export const useMyActions = (
  policies: Policy[],
  user: User | null,
): UseMyActionsReturn => {
  const actionablePolicies = useMemo((): Policy[] => {
    if (!user || !policies.length) {
      console.log('useMyActions: No user or no policies', {
        user: !!user,
        policiesCount: policies.length,
      });
      return [];
    }

    const userRole = user.role;
    const userId = user._id;

    console.log('useMyActions: Filtering policies', {
      userRole,
      userId,
      totalPolicies: policies.length,
      policyStatuses: policies.map((p) => ({
        id: p.policyId,
        status: p.status,
        owner: p.policyOwner?.id,
      })),
    });

    const filtered = policies.filter((policy) => {
      // Check if user can perform actions based on role and policy status
      switch (policy.status) {
        case 'Request Initiated':
          // Only Approvers from Governance department, Admins, and Super Admins can approve/reject policy requests
          return (
            (userRole === 'Approver' && user.department === 'Governance') ||
            ['Admin', 'Super Admin'].includes(userRole)
          );

        case 'Draft':
          // Only the policy owner (Creator) can work on drafts, plus Admins and Super Admins
          return ['Creator', 'Admin', 'Super Admin'].includes(userRole);
        //  &&
        // (userRole !== 'Creator' || policy.policyOwner?.id === userId)

        case 'Under Review':
          // Only Reviewers from GRC department, Admins, and Super Admins can review policies
          return (
            (userRole === 'Reviewer' && user.department === 'GRC') ||
            ['Admin', 'Super Admin'].includes(userRole)
          );

        case 'Pending Approval':
          // Only Approvers, Admins, and Super Admins can give final approval
          return ['Approver', 'Admin', 'Super Admin'].includes(userRole);

        case 'Approved':
          // Only Publishers, Admins, and Super Admins can publish policies
          return ['Publisher', 'Admin', 'Super Admin'].includes(userRole);

        case 'Published':
          // Publishers and Reviewers can start annual review, Admins and Super Admins can archive
          return ['Publisher', 'Reviewer', 'Admin', 'Super Admin'].includes(
            userRole,
          );

        // case 'Under Annual Review':
        //   // Reviewers can review, Creators can update their policies, Admins and Super Admins can manage
        //   return (
        //     ['Reviewer', 'Admin', 'Super Admin'].includes(userRole) ||
        //     (userRole === 'Creator' && policy.policyOwner?.id === userId)
        //   );

        default:
          return false;
      }
    });

    console.log('useMyActions: Filtered actionable policies', {
      actionableCount: filtered.length,
      actionablePolicies: filtered.map((p: Policy) => ({
        id: p.policyId,
        status: p.status,
        owner: p.policyOwner?.id,
      })),
    });

    return filtered;
  }, [policies, user]);

  const actionCategories = useMemo((): ActionCategories => {
    if (!user) {
      return {
        urgent: [],
        myDrafts: [],
        awaitingMyReview: [],
        awaitingMyApproval: [],
        readyToPublish: [],
        annualReview: [],
      };
    }

    const userRole = user.role;
    const userId = user._id;

    const categories: ActionCategories = {
      urgent: [],
      myDrafts: [],
      awaitingMyReview: [],
      awaitingMyApproval: [],
      readyToPublish: [],
      annualReview: [],
    };

    actionablePolicies.forEach((policy) => {
      //console.log('fvfvef',policy.policyOwner.id);
      // Check for urgent items (overdue or high priority)
      // Only consider overdue if policy is not yet published and due date has passed
      const isOverdue =
        policy.dueDate &&
        new Date(policy.dueDate) < new Date() &&
        !['Published', 'Archived'].includes(policy.status);

      if (isOverdue) {
        categories.urgent.push(policy);
      }

      // Categorize by action type
      switch (policy.status) {
        case 'Draft':
          // Only show drafts that belong to the current user (for Creators) or all drafts (for Admins/Super Admins)
          if (
            // (userRole === 'Creator' && (policy.policyOwner?.id === userId || policy.policyOwner?._id === userId)) ||
            ['Creator', 'Admin', 'Super Admin'].includes(userRole)
          ) {
            categories.myDrafts.push(policy);
          }
          break;

        case 'Request Initiated':
          // Only Approvers from Governance department, Admins, and Super Admins can review policy requests
          if (
            (userRole === 'Approver' && user.department === 'Governance') ||
            ['Admin', 'Super Admin'].includes(userRole)
          ) {
            categories.awaitingMyReview.push(policy);
          }
          break;

        case 'Under Review':
          // Only Reviewers from GRC department, Admins, and Super Admins can review policies
          if (
            (userRole === 'Reviewer' && user.department === 'GRC') ||
            ['Admin', 'Super Admin'].includes(userRole)
          ) {
            categories.awaitingMyReview.push(policy);
          }
          break;

        case 'Under Annual Review':
          // Reviewers can review, Creators can update their own policies
          if (
            ['Reviewer', 'Admin', 'Super Admin'].includes(userRole) ||
            (userRole === 'Creator' && policy.policyOwner?.id === userId)
          ) {
            categories.annualReview.push(policy);
          }
          break;

        case 'Pending Approval':
          // Only Approvers, Admins, and Super Admins can give final approval
          if (['Approver', 'Admin', 'Super Admin'].includes(userRole)) {
            categories.awaitingMyApproval.push(policy);
          }
          break;

        case 'Approved':
          // Only Publishers, Admins, and Super Admins can publish policies
          if (['Publisher', 'Admin', 'Super Admin'].includes(userRole)) {
            categories.readyToPublish.push(policy);
          }
          break;
      }
    });

    return categories;
  }, [actionablePolicies, user]);

  const totalActionCount = useMemo(() => {
    return Object.values(actionCategories).reduce(
      (total, category) => total + category.length,
      0,
    );
  }, [actionCategories]);

  const hasUrgentActions = useMemo(() => {
    return actionCategories.urgent.length > 0;
  }, [actionCategories.urgent]);

  return {
    actionablePolicies,
    actionCategories,
    totalActionCount,
    hasUrgentActions,
  };
};

// Helper function to get action type for a policy based on user role
export const getActionType = (
  policy: Policy,
  userRole: string,
  userId: string,
  userDepartment?: string,
): string | null => {
  switch (policy.status) {
    case 'Request Initiated':
      if (
        (userRole === 'Approver' && userDepartment === 'Governance') ||
        ['Admin', 'Super Admin'].includes(userRole)
      ) {
        return 'governance-review';
      }
      break;

    case 'Draft':
      if (
        (userRole === 'Creator' && policy.policyOwner?.id === userId) ||
        ['Admin', 'Super Admin'].includes(userRole)
      ) {
        return 'draft-edit';
      }
      break;

    case 'Under Review':
      if (
        (userRole === 'Reviewer' && userDepartment === 'GRC') ||
        ['Admin', 'Super Admin'].includes(userRole)
      ) {
        return 'grc-review';
      }
      break;

    case 'Pending Approval':
      if (['Approver', 'Admin', 'Super Admin'].includes(userRole)) {
        return 'final-approval';
      }
      break;

    case 'Approved':
      if (['Publisher', 'Admin', 'Super Admin'].includes(userRole)) {
        return 'publish';
      }
      break;

    case 'Published':
      if (
        ['Publisher', 'Reviewer', 'Admin', 'Super Admin'].includes(userRole)
      ) {
        return 'annual-review';
      }
      break;

    case 'Under Annual Review':
      if (
        ['Reviewer', 'Admin', 'Super Admin'].includes(userRole) ||
        (userRole === 'Creator' && policy.policyOwner?.id === userId)
      ) {
        return 'annual-review-action';
      }
      break;
  }

  return null;
};

// Helper function to get priority level for sorting
export const getPolicyPriority = (policy: Policy): number => {
  let priority = 0;

  // Overdue items get highest priority
  if (policy.dueDate && new Date(policy.dueDate) < new Date()) {
    priority += 1000;
  }

  // High priority score
  priority += (policy.priorityScore || 5) * 10;

  // Status-based priority
  switch (policy.status) {
    case 'Request Initiated':
      priority += 50;
      break;
    case 'Pending Approval':
      priority += 40;
      break;
    case 'Under Review':
      priority += 30;
      break;
    case 'Draft':
      priority += 20;
      break;
    case 'Approved':
      priority += 10;
      break;
  }

  return priority;
};
